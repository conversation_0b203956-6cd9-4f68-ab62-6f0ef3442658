/* PEPE Store - Dark Green Glowing Theme (Only Theme) */
:root {
    --bs-primary: #00ff88;
    --bs-secondary: #333333;
    --bs-success: #00ff88;
    --bs-info: #00ccff;
    --bs-warning: #ffaa00;
    --bs-danger: #ff3366;
    --bs-light: #1a1a1a;
    --bs-dark: #0a0a0a;
    --bs-body-bg: #0a0a0a;
    --bs-body-color: #ffffff;
    --bs-card-bg: #1e1e1e;
    --bs-border-color: #333333;
    --bs-navbar-bg: #0f0f0f;
    --bs-placeholder-color: #888888;

    /* PEPE Specific Variables */
    --pepe-primary: #00ff88;
    --pepe-primary-dark: #00cc6a;
    --pepe-primary-light: #33ffaa;
    --pepe-accent: #ff6b35;
    --pepe-glow-primary: 0 0 20px rgba(0, 255, 136, 0.5);
    --pepe-glow-secondary: 0 0 15px rgba(0, 255, 136, 0.3);
    --pepe-glow-accent: 0 0 25px rgba(255, 107, 53, 0.6);
}

/* Tables */
.table {
    --bs-table-bg: var(--bs-card-bg);
    --bs-table-border-color: var(--bs-border-color);
    color: var(--bs-body-color) !important;
}

/* Text Colors */
.text-muted {
    color: #6c757d !important;
}

[data-theme="dark"] .text-muted,
[data-theme="pepe"] .text-muted {
    color: #adb5bd !important;
}

/* Borders */
.border {
    border-color: var(--bs-border-color) !important;
}
.recaptcha-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

@media (max-width: 400px) {
  .g-recaptcha {
    transform: scale(0.85);
    transform-origin: center;
  }
}

@media (max-width: 320px) {
  .g-recaptcha {
    transform: scale(0.75);
  }
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 200px;
}

[data-theme="dark"] .hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

/* App Cards */
.app-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: 1px solid rgba(0,0,0,0.125);
}

.app-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

[data-theme="dark"] .app-card:hover {
    box-shadow: 0 4px 8px rgba(255,255,255,0.1);
}

.card-img-top-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,0.125);
}

[data-theme="dark"] .card-img-top-container {
    background-color: var(--bs-light);
    border-bottom-color: var(--bs-border-color);
}

.app-icon {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
    border-radius: 0.375rem;
}

.app-icon-placeholder {
    width: 80px;
    height: 80px;
    background: #e9ecef;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

[data-theme="dark"] .app-icon-placeholder {
    background: var(--bs-light);
    border-color: var(--bs-border-color);
    color: #adb5bd;
}

/* App Detail Page */
.app-detail-icon {
    max-width: 128px;
    max-height: 128px;
    object-fit: cover;
    border-radius: 0.5rem;
}

.app-detail-icon-placeholder {
    width: 128px;
    height: 128px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #6c757d;
    border-radius: 0.5rem;
    margin: 0 auto;
}

[data-theme="dark"] .app-detail-icon-placeholder {
    background: var(--bs-light);
    border-color: var(--bs-border-color);
    color: #adb5bd;
}

.screenshot-thumb {
    cursor: pointer;
    transition: transform 0.2s ease-in-out;
    border-radius: 0.375rem;
}

.screenshot-thumb:hover {
    transform: scale(1.05);
}

.related-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 0.375rem;
}

.related-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

[data-theme="dark"] .related-app-icon-placeholder {
    background: var(--bs-light);
    border-color: var(--bs-border-color);
    color: #adb5bd;
}

/* Admin Styles */
.admin-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 0.375rem;
}

.admin-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

[data-theme="dark"] .admin-app-icon-placeholder {
    background: var(--bs-light);
    border-color: var(--bs-border-color);
    color: #adb5bd;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 1rem !important;
    }

    .hero-section .display-4 {
        font-size: 2rem;
    }

    .card-img-top-container {
        height: 150px;
    }

    .app-icon-placeholder {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .hero-section .display-4 {
        font-size: 1.75rem;
    }

    .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Utility Classes */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Theme Toggle Button */
.theme-toggle {
    transition: transform 0.2s ease-in-out;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bs-dark);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
    background: #555;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
    background: #777;
}

/* PEPE Animations */
@keyframes pepe-bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes pepe-pulse-glow {
    0% { text-shadow: var(--pepe-glow-secondary); }
    100% { text-shadow: var(--pepe-glow-primary); }
}

@keyframes pepe-scan {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes pepe-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pepe-pulse-ring {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

@keyframes pepe-featured-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: var(--pepe-glow-accent); }
}

/* Global Body Styling */
body {
    background-color: var(--bs-body-bg) !important;
    color: var(--bs-body-color) !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Card Styling */
.card {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-border-color) !important;
    color: var(--bs-body-color) !important;
}
.form-text {
    color:var(--bs-body-color)
}

/* Form Controls */
.form-control, .form-select {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-border-color) !important;
    color: var(--bs-body-color) !important;       /* add text color */
}

.form-control:focus, .form-select:focus {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-primary) !important;
    color: var(--bs-body-color) !important;
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25) !important;
}

/* Style file input button */
input[type="file"].form-control::file-selector-button {
    background-color: var(--bs-secondary) !important;
    color: var(--bs-body-color) !important;
    border: none !important;
    padding: 0.375rem 0.75rem;
    margin-right: 0.5rem;
    cursor: pointer;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

input[type="file"].form-control::file-selector-button:hover {
    background-color: var(--bs-primary) !important;
    color: var(--bs-white) !important;
}

.form-control::placeholder {
    color: var(--bs-placeholder-color) !important;
}

/* Navbar */
.navbar {
    background-color: var(--bs-navbar-bg) !important;
}

/* Dropdown */
.dropdown-menu {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-border-color) !important;
}

.dropdown-item {
    color: var(--bs-body-color) !important;
}

.dropdown-item:hover {
    background-color: var(--bs-primary) !important;
    color: white !important;
}

/* Modal */
.modal-content {
    background-color: var(--bs-card-bg) !important;
    border-color: var(--bs-border-color) !important;
    color: var(--bs-body-color) !important;
}

.modal-header {
    border-bottom-color: var(--bs-border-color) !important;
}

.modal-footer {
    border-top-color: var(--bs-border-color) !important;
}

/* PEPE Theme Special Effects - Always Active */
body {
    background: linear-gradient(135deg, var(--bs-dark) 0%, var(--bs-light) 100%) !important;
}

.navbar {
    border-bottom: 2px solid var(--pepe-primary);
    box-shadow: var(--pepe-glow-secondary);
}

/* PEPE Glow Effects */
.pepe-glow {
    color: var(--pepe-primary);
    text-shadow: var(--pepe-glow-primary);
    animation: pepe-pulse-glow 2s ease-in-out infinite alternate;
}

.pepe-brand {
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none !important;
    color: var(--bs-body-color) !important;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.pepe-brand:hover {
    color: var(--pepe-primary) !important;
    text-shadow: var(--pepe-glow-secondary);
    transform: scale(1.05);
}

.pepe-logo {
    font-size: 2rem;
    animation: pepe-bounce 2s infinite;
    filter: drop-shadow(var(--pepe-glow-secondary));
}

.nav-link.active {
  color: white !important;
}

.pepe-tagline {
    font-size: 0.8rem;
    color: var(--pepe-primary);
    margin-left: 0.5rem;
    opacity: 0.8;
    text-shadow: var(--pepe-glow-secondary);
}

/* Hero Section with PEPE Effects */
.hero-section {
    background: linear-gradient(135deg, var(--bs-card-bg) 0%, var(--bs-light) 50%, var(--bs-card-bg) 100%);
    border: 2px solid var(--pepe-primary);
    box-shadow: var(--pepe-glow-primary), inset 0 0 50px rgba(0, 255, 136, 0.1);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    animation: pepe-scan 4s linear infinite;
    pointer-events: none;
}

.hero-section h1 {
    text-shadow: var(--pepe-glow-primary);
    color: var(--pepe-primary);
}

.hero-section h2 {
    color: var(--pepe-accent);
    text-shadow: var(--pepe-glow-accent);
}

/* App Cards with Glow */
.app-card {
    background: var(--bs-card-bg);
    border: 1px solid var(--bs-border-color);
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.app-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    transition: left 0.5s ease;
}

.app-card:hover {
    transform: translateY(-5px);
    border-color: var(--pepe-primary);
    box-shadow: var(--pepe-glow-primary);
}

.app-card:hover::before {
    left: 100%;
}

/* Buttons with Glow */
.btn-primary {
    background: linear-gradient(45deg, var(--pepe-primary), var(--pepe-primary-light));
    border: none;
    color: var(--bs-dark);
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover {
    background: linear-gradient(45deg, var(--pepe-primary-light), var(--pepe-primary));
    transform: scale(1.05);
    box-shadow: var(--pepe-glow-primary);
    color: var(--bs-dark);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

/* PEPE Theme Navigation Styling */
[data-theme="pepe"] .pepe-brand {
    font-size: 1.5rem;
    font-weight: bold;
    text-decoration: none !important;
    color: var(--bs-body-color) !important;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

[data-theme="pepe"] .pepe-brand:hover {
    color: var(--pepe-primary) !important;
    text-shadow: var(--pepe-glow-secondary);
    transform: scale(1.05);
}

[data-theme="pepe"] .pepe-logo {
    font-size: 2rem;
    animation: pepe-bounce 2s infinite;
}

[data-theme="pepe"] .pepe-tagline {
    font-size: 0.8rem;
    color: var(--pepe-primary);
    margin-left: 0.5rem;
    opacity: 0.8;
}

/* Hero Section */
.pepe-hero-section {
    background: linear-gradient(135deg, var(--pepe-bg-secondary) 0%, var(--pepe-bg-card) 50%, var(--pepe-bg-secondary) 100%);
    border: 2px solid var(--pepe-primary);
    box-shadow: var(--pepe-glow-primary), inset 0 0 50px rgba(0, 255, 136, 0.1);
    position: relative;
    overflow: hidden;
}

.pepe-hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    animation: pepe-scan 4s linear infinite;
    pointer-events: none;
}

.pepe-hero-title {
    text-shadow: var(--pepe-glow-primary);
    margin-bottom: 1rem;
}

.pepe-glow {
    color: var(--pepe-primary);
    text-shadow: var(--pepe-glow-primary);
    animation: pepe-pulse-glow 2s ease-in-out infinite alternate;
}

.pepe-subtitle {
    color: var(--pepe-accent);
    text-shadow: var(--pepe-glow-accent);
    font-weight: 600;
}

.pepe-hero-text {
    color: var(--pepe-text-secondary);
    font-size: 1.1rem;
}

.pepe-stats {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.pepe-stat-item {
    color: var(--pepe-primary);
    font-weight: 600;
    font-size: 1rem;
    text-shadow: var(--pepe-glow-secondary);
    animation: pepe-stat-glow 3s ease-in-out infinite alternate;
}

.pepe-hero-icon {
    position: relative;
    display: inline-block;
}

.pepe-mega-logo {
    font-size: 8rem;
    display: block;
    animation: pepe-float 3s ease-in-out infinite;
    filter: drop-shadow(var(--pepe-glow-primary));
}

.pepe-pulse-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    border: 2px solid var(--pepe-primary);
    border-radius: 50%;
    animation: pepe-pulse-ring 2s ease-out infinite;
}

.pepe-pulse-ring-2 {
    animation-delay: 1s;
    border-color: var(--pepe-accent);
}

/* App Cards */
.pepe-app-card {
    background: var(--pepe-bg-card);
    border: 1px solid var(--pepe-border);
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pepe-app-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.1), transparent);
    transition: left 0.5s ease;
}

.pepe-app-card:hover {
    transform: translateY(-5px);
    border-color: var(--pepe-primary);
    box-shadow: var(--pepe-glow-primary);
}

.pepe-app-card:hover::before {
    left: 100%;
}

.pepe-featured-card {
    border-color: var(--pepe-accent);
    box-shadow: var(--pepe-glow-accent);
}

.pepe-featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(45deg, var(--pepe-accent), var(--pepe-warning));
    color: var(--pepe-bg-primary);
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: bold;
    z-index: 10;
    animation: pepe-featured-pulse 2s ease-in-out infinite;
}

.pepe-app-title {
    color: var(--pepe-primary);
    font-weight: 600;
    text-shadow: var(--pepe-glow-secondary);
}

.pepe-app-desc {
    color: var(--pepe-text-secondary);
    font-size: 0.9rem;
}

.pepe-price {
    color: var(--pepe-success);
    font-weight: bold;
    text-shadow: var(--pepe-glow-secondary);
}

.pepe-category-badge {
    background: linear-gradient(45deg, var(--pepe-primary), var(--pepe-primary-light));
    color: var(--pepe-bg-primary);
    border: none;
    font-weight: 600;
}

.pepe-icon-placeholder {
    background: linear-gradient(45deg, var(--pepe-bg-secondary), var(--pepe-bg-card));
    border: 2px dashed var(--pepe-primary);
    color: var(--pepe-primary);
}

/* Buttons */
.pepe-btn-primary {
    background: linear-gradient(45deg, var(--pepe-primary), var(--pepe-primary-light));
    border: none;
    color: var(--pepe-bg-primary);
    font-weight: 600;
    border-radius: 25px;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.pepe-btn-primary:hover {
    background: linear-gradient(45deg, var(--pepe-primary-light), var(--pepe-primary));
    transform: scale(1.05);
    box-shadow: var(--pepe-glow-primary);
    color: var(--pepe-bg-primary);
}

.pepe-glow-btn {
    position: relative;
    overflow: hidden;
}

.pepe-glow-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.pepe-glow-btn:hover::before {
    left: 100%;
}

/* Footer */
.pepe-footer {
    background: linear-gradient(135deg, var(--pepe-bg-nav) 0%, var(--pepe-bg-secondary) 100%);
    border-top: 2px solid var(--pepe-primary);
    box-shadow: 0 -5px 20px rgba(0, 255, 136, 0.3);
}

.pepe-footer-title {
    color: var(--pepe-primary);
    text-shadow: var(--pepe-glow-secondary);
}

.pepe-footer-text {
    color: var(--pepe-text-secondary);
}

.pepe-footer-small {
    color: var(--pepe-text-muted);
    font-size: 0.8rem;
    opacity: 0.7;
}

/* Admin Dropdown */
.pepe-dropdown {
    background: var(--pepe-bg-card);
    border: 1px solid var(--pepe-primary);
    box-shadow: var(--pepe-glow-secondary);
}

.pepe-dropdown .dropdown-item {
    color: var(--pepe-text-primary);
    transition: all 0.3s ease;
}

.pepe-dropdown .dropdown-item:hover {
    background: var(--pepe-primary);
    color: var(--pepe-bg-primary);
}

.pepe-admin-link {
    color: var(--pepe-primary) !important;
    text-shadow: var(--pepe-glow-secondary);
}

/* Animations */
@keyframes pepe-bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes pepe-pulse-glow {
    0% { text-shadow: var(--pepe-glow-secondary); }
    100% { text-shadow: var(--pepe-glow-primary); }
}

@keyframes pepe-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes pepe-pulse-ring {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

@keyframes pepe-scan {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

@keyframes pepe-stat-glow {
    0% { opacity: 0.8; }
    100% { opacity: 1; text-shadow: var(--pepe-glow-primary); }
}

@keyframes pepe-featured-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: var(--pepe-glow-accent); }
}

/* Responsive Design for PEPE Theme */
@media (max-width: 768px) {
    .pepe-mega-logo {
        font-size: 4rem;
    }

    .pepe-stats {
        gap: 1rem;
        justify-content: center;
    }

    .pepe-stat-item {
        font-size: 0.9rem;
    }

    .pepe-tagline {
        display: none;
    }
}

@media (max-width: 576px) {
    .pepe-hero-title {
        font-size: 2rem;
    }

    .pepe-subtitle {
        font-size: 1.2rem;
    }

    .pepe-stats {
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }
}
