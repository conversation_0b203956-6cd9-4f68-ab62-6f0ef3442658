2025-06-10 23:24:13,425 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:24:13,426 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:24:13,433 INFO:  * Restarting with stat
2025-06-10 23:24:13,972 WARNING:  * Debugger is active!
2025-06-10 23:24:13,988 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:24:18,435 INFO: 1******** - - [10/Jun/2025 23:24:18] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 23:24:18,482 INFO: 1******** - - [10/Jun/2025 23:24:18] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:24:18,919 INFO: 1******** - - [10/Jun/2025 23:24:18] "GET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:24:18,920 INFO: 1******** - - [10/Jun/2025 23:24:18] "GET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:24:19,070 INFO: 1******** - - [10/Jun/2025 23:24:19] "GET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:24:19,277 INFO: 1******** - - [10/Jun/2025 23:24:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,211 INFO: 1******** - - [10/Jun/2025 23:26:19] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:19,453 INFO: 1******** - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,547 INFO: 1******** - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,562 INFO: 1******** - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,885 INFO: 1******** - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,138 INFO: 1******** - - [10/Jun/2025 23:26:33] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:33,471 INFO: 1******** - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,471 INFO: 1******** - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,736 INFO: 1******** - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,813 INFO: 1******** - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:35,875 INFO: 1******** - - [10/Jun/2025 23:26:35] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:36,204 INFO: 1******** - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,204 INFO: 1******** - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,451 INFO: 1******** - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,529 INFO: 1******** - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:40,238 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:26:40,239 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:26:40,246 INFO:  * Restarting with stat
2025-06-10 23:26:40,592 WARNING:  * Debugger is active!
2025-06-10 23:26:40,602 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:26:41,362 INFO: 1******** - - [10/Jun/2025 23:26:41] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:41,652 INFO: 1******** - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:41,688 INFO: 1******** - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:41,704 INFO: 1******** - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:42,034 INFO: 1******** - - [10/Jun/2025 23:26:42] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,108 INFO: 1******** - - [10/Jun/2025 23:26:59] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:59,433 INFO: 1******** - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,433 INFO: 1******** - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,699 INFO: 1******** - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,761 INFO: 1******** - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,393 INFO: 1******** - - [10/Jun/2025 23:27:00] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:27:00,722 INFO: 1******** - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,722 INFO: 1******** - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,988 INFO: 1******** - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,051 INFO: 1******** - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,317 INFO: 1******** - - [10/Jun/2025 23:27:01] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:27:01,593 INFO: 1******** - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,640 INFO: 1******** - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,657 INFO: 1******** - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,980 INFO: 1******** - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,203 INFO: 1******** - - [10/Jun/2025 23:27:24] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:27:24,534 INFO: 1******** - - [10/Jun/2025 23:27:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,535 INFO: 1******** - - [10/Jun/2025 23:27:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,794 INFO: 1******** - - [10/Jun/2025 23:27:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:27:28,359 INFO: 1******** - - [10/Jun/2025 23:27:28] "[35m[1mGET /admin/apps HTTP/1.1[0m" 500 -
2025-06-10 23:27:28,685 INFO: 1******** - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:27:28,690 INFO: 1******** - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:27:28,947 INFO: 1******** - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:27:29,008 INFO: 1******** - - [10/Jun/2025 23:27:29] "[36mGET /admin/apps?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:05,377 INFO: 1******** - - [10/Jun/2025 23:34:05] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-10 23:34:05,445 INFO: 1******** - - [10/Jun/2025 23:34:05] "GET /gate HTTP/1.1" 200 -
2025-06-10 23:34:05,515 INFO: 1******** - - [10/Jun/2025 23:34:05] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 23:34:05,519 INFO: 1******** - - [10/Jun/2025 23:34:05] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 23:34:05,520 INFO: 1******** - - [10/Jun/2025 23:34:05] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-10 23:34:05,958 INFO: 1******** - - [10/Jun/2025 23:34:05] "POST /registerf HTTP/1.1" 200 -
2025-06-10 23:34:05,996 INFO: 1******** - - [10/Jun/2025 23:34:05] "GET / HTTP/1.1" 200 -
2025-06-10 23:34:06,031 INFO: 1******** - - [10/Jun/2025 23:34:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:06,032 INFO: 1******** - - [10/Jun/2025 23:34:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:08,233 INFO: 1******** - - [10/Jun/2025 23:34:08] "GET /login HTTP/1.1" 200 -
2025-06-10 23:34:08,258 INFO: 1******** - - [10/Jun/2025 23:34:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:08,258 INFO: 1******** - - [10/Jun/2025 23:34:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:13,273 INFO: 1******** - - [10/Jun/2025 23:34:13] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 23:34:13,292 INFO: 1******** - - [10/Jun/2025 23:34:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:34:13,406 INFO: 1******** - - [10/Jun/2025 23:34:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:13,406 INFO: 1******** - - [10/Jun/2025 23:34:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:16,502 INFO: 1******** - - [10/Jun/2025 23:34:16] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:34:16,536 INFO: 1******** - - [10/Jun/2025 23:34:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:16,536 INFO: 1******** - - [10/Jun/2025 23:34:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:19,671 INFO: 1******** - - [10/Jun/2025 23:34:19] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:19,696 INFO: 1******** - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:34:19,696 INFO: 1******** - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:34:19,708 INFO: 1******** - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:34:19,743 INFO: 1******** - - [10/Jun/2025 23:34:19] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,363 INFO: 1******** - - [10/Jun/2025 23:34:36] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:36,381 INFO: 1******** - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,381 INFO: 1******** - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,400 INFO: 1******** - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,415 INFO: 1******** - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,074 INFO: 1******** - - [10/Jun/2025 23:34:37] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:37,089 INFO: 1******** - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,090 INFO: 1******** - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,099 INFO: 1******** - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,106 INFO: 1******** - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:54,134 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:34:54,135 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:34:54,144 INFO:  * Restarting with stat
2025-06-10 23:34:54,565 WARNING:  * Debugger is active!
2025-06-10 23:34:54,580 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:34:56,498 INFO: 1******** - - [10/Jun/2025 23:34:56] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:56,615 INFO: 1******** - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,616 INFO: 1******** - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,634 INFO: 1******** - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,647 INFO: 1******** - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,203 INFO: 1******** - - [10/Jun/2025 23:34:57] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:57,221 INFO: 1******** - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,221 INFO: 1******** - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,244 INFO: 1******** - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,255 INFO: 1******** - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:35:18,773 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:35:18,774 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:35:18,783 INFO:  * Restarting with stat
2025-06-10 23:35:19,143 WARNING:  * Debugger is active!
2025-06-10 23:35:19,152 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:35:19,207 INFO: 1******** - - [10/Jun/2025 23:35:19] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-10 23:35:19,375 INFO: 1******** - - [10/Jun/2025 23:35:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:19,375 INFO: 1******** - - [10/Jun/2025 23:35:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:19,511 INFO: 1******** - - [10/Jun/2025 23:35:19] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-10 23:35:30,117 INFO: 1******** - - [10/Jun/2025 23:35:30] "[32mPOST /admin/add_user HTTP/1.1[0m" 302 -
2025-06-10 23:35:30,142 INFO: 1******** - - [10/Jun/2025 23:35:30] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:30,174 INFO: 1******** - - [10/Jun/2025 23:35:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:30,174 INFO: 1******** - - [10/Jun/2025 23:35:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,406 INFO: 1******** - - [10/Jun/2025 23:35:32] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:32,434 INFO: 1******** - - [10/Jun/2025 23:35:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,435 INFO: 1******** - - [10/Jun/2025 23:35:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,485 INFO: 1******** - - [10/Jun/2025 23:35:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,102 INFO: 1******** - - [10/Jun/2025 23:35:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:33,127 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,127 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,170 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,622 INFO: 1******** - - [10/Jun/2025 23:35:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:33,658 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,659 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,703 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,960 INFO: 1******** - - [10/Jun/2025 23:35:33] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-10 23:35:33,989 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,990 INFO: 1******** - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:36,111 INFO: 1******** - - [10/Jun/2025 23:35:36] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:36,145 INFO: 1******** - - [10/Jun/2025 23:35:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:36,147 INFO: 1******** - - [10/Jun/2025 23:35:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:43,147 INFO: 1******** - - [10/Jun/2025 23:36:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:45,713 INFO: 1******** - - [10/Jun/2025 23:36:45] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:36:45,762 INFO: 1******** - - [10/Jun/2025 23:36:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:36:45,774 INFO: 1******** - - [10/Jun/2025 23:36:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:46,179 INFO: 1******** - - [10/Jun/2025 23:36:46] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:37:32,917 INFO: 1******** - - [10/Jun/2025 23:37:32] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:37:32,971 INFO: 1******** - - [10/Jun/2025 23:37:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:37:32,974 INFO: 1******** - - [10/Jun/2025 23:37:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,222 INFO: 1******** - - [10/Jun/2025 23:37:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,641 INFO: 1******** - - [10/Jun/2025 23:37:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:37:33,665 INFO: 1******** - - [10/Jun/2025 23:37:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,666 INFO: 1******** - - [10/Jun/2025 23:37:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,709 INFO: 1******** - - [10/Jun/2025 23:37:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:41:26,089 INFO: 1******** - - [10/Jun/2025 23:41:26] "[35m[1mGET /admin/users HTTP/1.1[0m" 500 -
2025-06-10 23:41:26,138 INFO: 1******** - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:41:26,138 INFO: 1******** - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:41:26,195 INFO: 1******** - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:41:26,234 INFO: 1******** - - [10/Jun/2025 23:41:26] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,829 INFO: 1******** - - [10/Jun/2025 23:42:03] "[35m[1mGET /admin/users HTTP/1.1[0m" 500 -
2025-06-10 23:42:03,855 INFO: 1******** - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,855 INFO: 1******** - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,879 INFO: 1******** - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,896 INFO: 1******** - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:43:01,963 INFO: 1******** - - [10/Jun/2025 23:43:01] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:43:01,991 INFO: 1******** - - [10/Jun/2025 23:43:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:43:01,991 INFO: 1******** - - [10/Jun/2025 23:43:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:43:02,067 INFO: 1******** - - [10/Jun/2025 23:43:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:44:27,550 INFO: 1******** - - [10/Jun/2025 23:44:27] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:44:27,586 INFO: 1******** - - [10/Jun/2025 23:44:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:44:27,586 INFO: 1******** - - [10/Jun/2025 23:44:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:44:29,841 INFO: 1******** - - [10/Jun/2025 23:44:29] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:44:29,872 INFO: 1******** - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:44:29,879 INFO: 1******** - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:44:29,891 INFO: 1******** - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:44:29,900 INFO: 1******** - - [10/Jun/2025 23:44:29] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,402 INFO: 1******** - - [10/Jun/2025 23:45:21] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:45:21,424 INFO: 1******** - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,424 INFO: 1******** - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,512 INFO: 1******** - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,526 INFO: 1******** - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,377 INFO: 1******** - - [10/Jun/2025 23:45:22] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:45:22,400 INFO: 1******** - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,400 INFO: 1******** - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,419 INFO: 1******** - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,434 INFO: 1******** - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:47:40,025 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:47:40,027 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:47:40,037 INFO:  * Restarting with stat
2025-06-10 23:47:40,495 WARNING:  * Debugger is active!
2025-06-10 23:47:40,508 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:47:43,993 INFO: 1******** - - [10/Jun/2025 23:47:43] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:47:44,131 INFO: 1******** - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,133 INFO: 1******** - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,150 INFO: 1******** - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,165 INFO: 1******** - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:04,381 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:48:04,382 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:48:04,387 INFO:  * Restarting with stat
2025-06-10 23:48:04,762 WARNING:  * Debugger is active!
2025-06-10 23:48:04,779 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:48:08,280 INFO: 1******** - - [10/Jun/2025 23:48:08] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:48:08,405 INFO: 1******** - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,413 INFO: 1******** - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,429 INFO: 1******** - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,451 INFO: 1******** - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:52,251 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:48:52,252 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:48:52,258 INFO:  * Restarting with stat
2025-06-10 23:48:52,635 WARNING:  * Debugger is active!
2025-06-10 23:48:52,650 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:48:59,029 INFO: 1******** - - [10/Jun/2025 23:48:59] "GET /admin/logs HTTP/1.1" 200 -
2025-06-10 23:48:59,159 INFO: 1******** - - [10/Jun/2025 23:48:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:48:59,160 INFO: 1******** - - [10/Jun/2025 23:48:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:48:59,628 INFO: 1******** - - [10/Jun/2025 23:48:59] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:49:01,893 INFO: 1******** - - [10/Jun/2025 23:49:01] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:49:01,928 INFO: 1******** - - [10/Jun/2025 23:49:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:01,928 INFO: 1******** - - [10/Jun/2025 23:49:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:02,545 INFO: 1******** - - [10/Jun/2025 23:49:02] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:49:02,573 INFO: 1******** - - [10/Jun/2025 23:49:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:02,574 INFO: 1******** - - [10/Jun/2025 23:49:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,061 INFO: 1******** - - [10/Jun/2025 23:49:03] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:49:03,086 INFO: 1******** - - [10/Jun/2025 23:49:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,087 INFO: 1******** - - [10/Jun/2025 23:49:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,776 INFO: 1******** - - [10/Jun/2025 23:49:03] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:49:03,805 INFO: 1******** - - [10/Jun/2025 23:49:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,806 INFO: 1******** - - [10/Jun/2025 23:49:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:51:42,678 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:51:42,680 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:51:42,688 INFO:  * Restarting with stat
2025-06-10 23:51:43,077 WARNING:  * Debugger is active!
2025-06-10 23:51:43,093 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:51:47,528 INFO: 1******** - - [10/Jun/2025 23:51:47] "[35m[1mGET /admin/logs?type=activity HTTP/1.1[0m" 500 -
2025-06-10 23:51:47,694 INFO: 1******** - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,694 INFO: 1******** - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,708 INFO: 1******** - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,730 INFO: 1******** - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:52:25,864 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:52:25,866 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:52:25,875 INFO:  * Restarting with stat
2025-06-10 23:52:26,312 WARNING:  * Debugger is active!
2025-06-10 23:52:26,326 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:52:27,570 INFO: 1******** - - [10/Jun/2025 23:52:27] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:52:27,710 INFO: 1******** - - [10/Jun/2025 23:52:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:27,714 INFO: 1******** - - [10/Jun/2025 23:52:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:27,807 INFO: 1******** - - [10/Jun/2025 23:52:27] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:52:38,568 INFO: 1******** - - [10/Jun/2025 23:52:38] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:52:38,602 INFO: 1******** - - [10/Jun/2025 23:52:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:38,603 INFO: 1******** - - [10/Jun/2025 23:52:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:40,154 INFO: 1******** - - [10/Jun/2025 23:52:40] "GET /admin/logs HTTP/1.1" 200 -
2025-06-10 23:52:40,188 INFO: 1******** - - [10/Jun/2025 23:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:40,188 INFO: 1******** - - [10/Jun/2025 23:52:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:41,153 INFO: 1******** - - [10/Jun/2025 23:52:41] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:52:41,184 INFO: 1******** - - [10/Jun/2025 23:52:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:41,185 INFO: 1******** - - [10/Jun/2025 23:52:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,063 INFO: 1******** - - [10/Jun/2025 23:52:43] "[35m[1mGET /admin/logs?type=download HTTP/1.1[0m" 500 -
2025-06-10 23:52:43,105 INFO: 1******** - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,108 INFO: 1******** - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,130 INFO: 1******** - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,153 INFO: 1******** - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:55:22,423 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:55:22,426 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:55:22,439 INFO:  * Restarting with stat
2025-06-10 23:55:22,955 WARNING:  * Debugger is active!
2025-06-10 23:55:22,970 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:55:24,276 INFO: 1******** - - [10/Jun/2025 23:55:24] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:55:24,724 INFO: 1******** - - [10/Jun/2025 23:55:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:24,725 INFO: 1******** - - [10/Jun/2025 23:55:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:24,899 INFO: 1******** - - [10/Jun/2025 23:55:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,093 INFO: 1******** - - [10/Jun/2025 23:55:27] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:55:27,134 INFO: 1******** - - [10/Jun/2025 23:55:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,134 INFO: 1******** - - [10/Jun/2025 23:55:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,947 INFO: 1******** - - [10/Jun/2025 23:55:27] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:55:27,987 INFO: 1******** - - [10/Jun/2025 23:55:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,988 INFO: 1******** - - [10/Jun/2025 23:55:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:29,233 INFO: 1******** - - [10/Jun/2025 23:55:29] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:55:29,278 INFO: 1******** - - [10/Jun/2025 23:55:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:29,279 INFO: 1******** - - [10/Jun/2025 23:55:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:30,825 INFO: 1******** - - [10/Jun/2025 23:55:30] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:55:30,863 INFO: 1******** - - [10/Jun/2025 23:55:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:30,865 INFO: 1******** - - [10/Jun/2025 23:55:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:31,405 INFO: 1******** - - [10/Jun/2025 23:55:31] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:55:31,441 INFO: 1******** - - [10/Jun/2025 23:55:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:31,442 INFO: 1******** - - [10/Jun/2025 23:55:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:32,027 INFO: 1******** - - [10/Jun/2025 23:55:32] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:55:32,081 INFO: 1******** - - [10/Jun/2025 23:55:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:32,082 INFO: 1******** - - [10/Jun/2025 23:55:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:33,181 INFO: 1******** - - [10/Jun/2025 23:55:33] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:55:33,221 INFO: 1******** - - [10/Jun/2025 23:55:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:33,223 INFO: 1******** - - [10/Jun/2025 23:55:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:35,893 INFO: 1******** - - [10/Jun/2025 23:55:35] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:55:35,928 INFO: 1******** - - [10/Jun/2025 23:55:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:35,930 INFO: 1******** - - [10/Jun/2025 23:55:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:37,015 INFO: 1******** - - [10/Jun/2025 23:55:37] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:55:37,055 INFO: 1******** - - [10/Jun/2025 23:55:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:37,056 INFO: 1******** - - [10/Jun/2025 23:55:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:31,071 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-10 23:59:31,072 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:59:31,085 INFO:  * Restarting with stat
2025-06-10 23:59:31,657 WARNING:  * Debugger is active!
2025-06-10 23:59:31,676 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:59:32,729 INFO: 1******** - - [10/Jun/2025 23:59:32] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:59:32,880 INFO: 1******** - - [10/Jun/2025 23:59:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:32,881 INFO: 1******** - - [10/Jun/2025 23:59:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:32,970 INFO: 1******** - - [10/Jun/2025 23:59:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:59:54,685 INFO: 1******** - - [10/Jun/2025 23:59:54] "[32mPOST /admin/add_app HTTP/1.1[0m" 302 -
2025-06-10 23:59:54,715 INFO: 1******** - - [10/Jun/2025 23:59:54] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:59:54,755 INFO: 1******** - - [10/Jun/2025 23:59:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:54,756 INFO: 1******** - - [10/Jun/2025 23:59:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:56,937 INFO: 1******** - - [10/Jun/2025 23:59:56] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:59:56,971 INFO: 1******** - - [10/Jun/2025 23:59:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:56,971 INFO: 1******** - - [10/Jun/2025 23:59:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:57,035 INFO: 1******** - - [10/Jun/2025 23:59:57] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 00:00:13,837 INFO: 1******** - - [11/Jun/2025 00:00:13] "GET / HTTP/1.1" 200 -
2025-06-11 00:00:14,157 INFO: 1******** - - [11/Jun/2025 00:00:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 00:00:14,157 INFO: 1******** - - [11/Jun/2025 00:00:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 00:02:07,049 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 00:02:07,049 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:02:07,058 INFO:  * Restarting with stat
2025-06-11 00:02:07,415 WARNING:  * Debugger is active!
2025-06-11 00:02:07,428 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:02:08,376 INFO: 1******** - - [11/Jun/2025 00:02:08] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-11 00:02:08,572 INFO: 1******** - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,581 INFO: 1******** - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,654 INFO: 1******** - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,665 INFO: 1******** - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:02:40,776 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 00:02:40,853 INFO:  * Restarting with stat
2025-06-11 00:04:10,743 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 00:04:10,743 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:04:10,749 INFO:  * Restarting with stat
2025-06-11 00:04:11,109 WARNING:  * Debugger is active!
2025-06-11 00:04:11,122 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:04:12,661 INFO: 1******** - - [11/Jun/2025 00:04:12] "GET / HTTP/1.1" 200 -
2025-06-11 00:04:13,017 INFO: 1******** - - [11/Jun/2025 00:04:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 00:04:13,019 INFO: 1******** - - [11/Jun/2025 00:04:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 00:04:13,729 INFO: 1******** - - [11/Jun/2025 00:04:13] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 00:04:15,106 INFO: 1******** - - [11/Jun/2025 00:04:15] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:04:15,134 INFO: 1******** - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-11 00:04:15,147 INFO: 1******** - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-11 00:04:15,211 INFO: 1******** - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-11 00:04:15,226 INFO: 1******** - - [11/Jun/2025 00:04:15] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:31,563 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 00:08:31,564 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:08:31,574 INFO:  * Restarting with stat
2025-06-11 00:08:31,937 WARNING:  * Debugger is active!
2025-06-11 00:08:31,946 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:08:35,989 INFO: 1******** - - [11/Jun/2025 00:08:35] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:08:36,138 INFO: 1******** - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,139 INFO: 1******** - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,152 INFO: 1******** - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,209 INFO: 1******** - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,898 INFO: 1******** - - [11/Jun/2025 00:08:44] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:08:44,927 INFO: 1******** - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,927 INFO: 1******** - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,955 INFO: 1******** - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,969 INFO: 1******** - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,462 INFO: 1******** - - [11/Jun/2025 00:10:07] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:10:07,483 INFO: 1******** - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,484 INFO: 1******** - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,509 INFO: 1******** - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,541 INFO: 1******** - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:30,774 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 00:10:30,774 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:10:30,796 INFO:  * Restarting with stat
2025-06-11 00:10:31,246 WARNING:  * Debugger is active!
2025-06-11 00:10:31,264 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:10:33,466 INFO: 1******** - - [11/Jun/2025 00:10:33] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:10:33,653 INFO: 1******** - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,668 INFO: 1******** - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,701 INFO: 1******** - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,717 INFO: 1******** - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 01:06:09,594 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:06:09,595 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:06:16,122 INFO: 1******** - - [11/Jun/2025 01:06:16] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:16,279 INFO: 1******** - - [11/Jun/2025 01:06:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:16,280 INFO: 1******** - - [11/Jun/2025 01:06:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:19,085 INFO: 1******** - - [11/Jun/2025 01:06:19] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:06:19,098 INFO: 1******** - - [11/Jun/2025 01:06:19] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:19,150 INFO: 1******** - - [11/Jun/2025 01:06:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:19,150 INFO: 1******** - - [11/Jun/2025 01:06:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,426 INFO: 1******** - - [11/Jun/2025 01:06:22] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:22,464 INFO: 1******** - - [11/Jun/2025 01:06:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,464 INFO: 1******** - - [11/Jun/2025 01:06:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,517 INFO: 1******** - - [11/Jun/2025 01:06:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:06:24,728 INFO: 1******** - - [11/Jun/2025 01:06:24] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:06:24,738 INFO: 1******** - - [11/Jun/2025 01:06:24] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:24,799 INFO: 1******** - - [11/Jun/2025 01:06:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:24,800 INFO: 1******** - - [11/Jun/2025 01:06:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:08:25,938 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:08:25,939 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:08:53,267 INFO: 1******** - - [11/Jun/2025 01:08:53] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:08:53,325 INFO: 1******** - - [11/Jun/2025 01:08:53] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:08:53,596 INFO: 1******** - - [11/Jun/2025 01:08:53] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 01:08:53,724 INFO: 1******** - - [11/Jun/2025 01:08:53] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 01:08:53,729 INFO: 1******** - - [11/Jun/2025 01:08:53] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-11 01:08:54,155 INFO: 1******** - - [11/Jun/2025 01:08:54] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:08:54,483 INFO: 1******** - - [11/Jun/2025 01:08:54] "GET / HTTP/1.1" 200 -
2025-06-11 01:08:54,495 INFO: 1******** - - [11/Jun/2025 01:08:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:08:54,714 INFO: 1******** - - [11/Jun/2025 01:08:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:08:55,106 INFO: 1******** - - [11/Jun/2025 01:08:55] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-11 01:14:53,449 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:14:53,450 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:14:54,477 INFO: 1******** - - [11/Jun/2025 01:14:54] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:14:54,506 INFO: 1******** - - [11/Jun/2025 01:14:54] "GET / HTTP/1.1" 200 -
2025-06-11 01:14:54,664 INFO: 1******** - - [11/Jun/2025 01:14:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:14:54,665 INFO: 1******** - - [11/Jun/2025 01:14:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:14:54,834 INFO: 1******** - - [11/Jun/2025 01:14:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:20:39,397 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:20:39,398 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:21:10,338 INFO: 1******** - - [11/Jun/2025 01:21:10] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:21:10,611 INFO: 1******** - - [11/Jun/2025 01:21:10] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:21:10,739 INFO: 1******** - - [11/Jun/2025 01:21:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:10,990 INFO: 1******** - - [11/Jun/2025 01:21:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:10,990 INFO: 1******** - - [11/Jun/2025 01:21:10] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:11,322 INFO: 1******** - - [11/Jun/2025 01:21:11] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:21:11,581 INFO: 1******** - - [11/Jun/2025 01:21:11] "GET / HTTP/1.1" 200 -
2025-06-11 01:21:11,658 INFO: 1******** - - [11/Jun/2025 01:21:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:11,940 INFO: 1******** - - [11/Jun/2025 01:21:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:25,172 INFO: 1******** - - [11/Jun/2025 01:21:25] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:21:25,410 INFO: 1******** - - [11/Jun/2025 01:21:25] "GET / HTTP/1.1" 200 -
2025-06-11 01:21:25,505 INFO: 1******** - - [11/Jun/2025 01:21:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:25,808 INFO: 1******** - - [11/Jun/2025 01:21:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:29,676 INFO: 1******** - - [11/Jun/2025 01:21:29] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:21:29,942 INFO: 1******** - - [11/Jun/2025 01:21:29] "GET / HTTP/1.1" 200 -
2025-06-11 01:21:29,998 INFO: 1******** - - [11/Jun/2025 01:21:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:30,276 INFO: 1******** - - [11/Jun/2025 01:21:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:26:30,780 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:26:30,781 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:26:59,548 INFO: 1******** - - [11/Jun/2025 01:26:59] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:26:59,821 INFO: 1******** - - [11/Jun/2025 01:26:59] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:27:00,084 INFO: 1******** - - [11/Jun/2025 01:27:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,150 INFO: 1******** - - [11/Jun/2025 01:27:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,151 INFO: 1******** - - [11/Jun/2025 01:27:00] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,249 INFO: 1******** - - [11/Jun/2025 01:27:00] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:27:00,600 INFO: 1******** - - [11/Jun/2025 01:27:00] "GET / HTTP/1.1" 200 -
2025-06-11 01:27:00,844 INFO: 1******** - - [11/Jun/2025 01:27:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,938 INFO: 1******** - - [11/Jun/2025 01:27:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:28:23,854 INFO: 1******** - - [11/Jun/2025 01:28:23] "[32mGET /app/2 HTTP/1.1[0m" 302 -
2025-06-11 01:28:24,079 INFO: 1******** - - [11/Jun/2025 01:28:24] "GET / HTTP/1.1" 200 -
2025-06-11 01:28:24,172 INFO: 1******** - - [11/Jun/2025 01:28:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:28:24,430 INFO: 1******** - - [11/Jun/2025 01:28:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:31:22,496 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:31:22,497 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:31:47,912 INFO: 1******** - - [11/Jun/2025 01:31:47] "GET / HTTP/1.1" 200 -
2025-06-11 01:31:48,202 INFO: 1******** - - [11/Jun/2025 01:31:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:31:48,252 INFO: 1******** - - [11/Jun/2025 01:31:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:32:16,718 INFO: 1******** - - [11/Jun/2025 01:32:16] "[32mGET /app/6 HTTP/1.1[0m" 302 -
2025-06-11 01:32:16,816 INFO: 1******** - - [11/Jun/2025 01:32:16] "GET / HTTP/1.1" 200 -
2025-06-11 01:32:17,038 INFO: 1******** - - [11/Jun/2025 01:32:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:32:17,210 INFO: 1******** - - [11/Jun/2025 01:32:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:35:54,414 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:35:54,415 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:36:16,353 INFO: 1******** - - [11/Jun/2025 01:36:16] "GET / HTTP/1.1" 200 -
2025-06-11 01:36:16,631 INFO: 1******** - - [11/Jun/2025 01:36:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:16,687 INFO: 1******** - - [11/Jun/2025 01:36:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:25,014 INFO: 1******** - - [11/Jun/2025 01:36:25] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:36:25,219 INFO: 1******** - - [11/Jun/2025 01:36:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:25,357 INFO: 1******** - - [11/Jun/2025 01:36:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:34,224 INFO: 1******** - - [11/Jun/2025 01:36:34] "[32mPOST /app/6/rate HTTP/1.1[0m" 302 -
2025-06-11 01:36:34,488 INFO: 1******** - - [11/Jun/2025 01:36:34] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:36:34,547 INFO: 1******** - - [11/Jun/2025 01:36:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:34,826 INFO: 1******** - - [11/Jun/2025 01:36:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:39,617 INFO: 1******** - - [11/Jun/2025 01:36:39] "[32mPOST /app/6/rate HTTP/1.1[0m" 302 -
2025-06-11 01:36:39,939 INFO: 1******** - - [11/Jun/2025 01:36:39] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:36:40,197 INFO: 1******** - - [11/Jun/2025 01:36:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:40,275 INFO: 1******** - - [11/Jun/2025 01:36:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:56,506 INFO: 1******** - - [11/Jun/2025 01:36:56] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:37:02,003 INFO: 1******** - - [11/Jun/2025 01:37:02] "GET / HTTP/1.1" 200 -
2025-06-11 01:37:02,342 INFO: 1******** - - [11/Jun/2025 01:37:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:37:02,343 INFO: 1******** - - [11/Jun/2025 01:37:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:47:17,068 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 01:47:17,084 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:52:39,444 INFO: 1******** - - [11/Jun/2025 01:52:39] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:52:39,721 INFO: 1******** - - [11/Jun/2025 01:52:39] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:52:39,849 INFO: 1******** - - [11/Jun/2025 01:52:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,097 INFO: 1******** - - [11/Jun/2025 01:52:40] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,105 INFO: 1******** - - [11/Jun/2025 01:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,485 INFO: 1******** - - [11/Jun/2025 01:52:40] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:52:40,593 INFO: 1******** - - [11/Jun/2025 01:52:40] "GET / HTTP/1.1" 200 -
2025-06-11 01:52:40,742 INFO: 1******** - - [11/Jun/2025 01:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,821 INFO: 1******** - - [11/Jun/2025 01:52:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,930 INFO: 1******** - - [11/Jun/2025 01:52:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,972 INFO: 1******** - - [11/Jun/2025 01:52:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:53:06,261 INFO: 1******** - - [11/Jun/2025 01:53:06] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:53:06,468 INFO: 1******** - - [11/Jun/2025 01:53:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:06,608 INFO: 1******** - - [11/Jun/2025 01:53:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:10,923 INFO: 1******** - - [11/Jun/2025 01:53:10] "GET /suggestions HTTP/1.1" 200 -
2025-06-11 01:53:11,260 INFO: 1******** - - [11/Jun/2025 01:53:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:11,260 INFO: 1******** - - [11/Jun/2025 01:53:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:23,328 INFO: 1******** - - [11/Jun/2025 01:53:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:23,917 INFO: 1******** - - [11/Jun/2025 01:53:23] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:53:24,250 INFO: 1******** - - [11/Jun/2025 01:53:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:24,251 INFO: 1******** - - [11/Jun/2025 01:53:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:24,513 INFO: 1******** - - [11/Jun/2025 01:53:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:53:30,388 INFO: 1******** - - [11/Jun/2025 01:53:30] "GET / HTTP/1.1" 200 -
2025-06-11 01:53:30,628 INFO: 1******** - - [11/Jun/2025 01:53:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:30,738 INFO: 1******** - - [11/Jun/2025 01:53:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:44,848 INFO: 1******** - - [11/Jun/2025 01:53:44] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:53:45,100 INFO: 1******** - - [11/Jun/2025 01:53:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:45,193 INFO: 1******** - - [11/Jun/2025 01:53:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:55:13,417 INFO: 1******** - - [11/Jun/2025 01:55:13] "GET /login HTTP/1.1" 200 -
2025-06-11 01:55:13,682 INFO: 1******** - - [11/Jun/2025 01:55:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:55:13,760 INFO: 1******** - - [11/Jun/2025 01:55:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:55:18,808 INFO: 1******** - - [11/Jun/2025 01:55:18] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 01:55:18,856 INFO: 1******** - - [11/Jun/2025 01:55:18] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 01:55:19,181 INFO: 1******** - - [11/Jun/2025 01:55:19] "GET / HTTP/1.1" 200 -
2025-06-11 01:55:19,440 INFO: 1******** - - [11/Jun/2025 01:55:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:55:19,520 INFO: 1******** - - [11/Jun/2025 01:55:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:02:58,527 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:02:58,529 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:04:40,561 INFO: 1******** - - [11/Jun/2025 02:04:40] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 02:04:40,831 INFO: 1******** - - [11/Jun/2025 02:04:40] "GET /gate HTTP/1.1" 200 -
2025-06-11 02:04:40,932 INFO: 1******** - - [11/Jun/2025 02:04:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,166 INFO: 1******** - - [11/Jun/2025 02:04:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,166 INFO: 1******** - - [11/Jun/2025 02:04:41] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,198 INFO: 1******** - - [11/Jun/2025 02:04:41] "POST /registerf HTTP/1.1" 200 -
2025-06-11 02:04:41,532 INFO: 1******** - - [11/Jun/2025 02:04:41] "GET / HTTP/1.1" 200 -
2025-06-11 02:04:41,533 INFO: 1******** - - [11/Jun/2025 02:04:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,772 INFO: 1******** - - [11/Jun/2025 02:04:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,863 INFO: 1******** - - [11/Jun/2025 02:04:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,904 INFO: 1******** - - [11/Jun/2025 02:04:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:04:44,304 INFO: 1******** - - [11/Jun/2025 02:04:44] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 02:04:44,504 INFO: 1******** - - [11/Jun/2025 02:04:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:44,642 INFO: 1******** - - [11/Jun/2025 02:04:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:50,033 INFO: 1******** - - [11/Jun/2025 02:04:50] "GET /login HTTP/1.1" 200 -
2025-06-11 02:04:50,298 INFO: 1******** - - [11/Jun/2025 02:04:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:50,378 INFO: 1******** - - [11/Jun/2025 02:04:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:53,494 INFO: 1******** - - [11/Jun/2025 02:04:53] "POST /login HTTP/1.1" 200 -
2025-06-11 02:04:53,820 INFO: 1******** - - [11/Jun/2025 02:04:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:53,823 INFO: 1******** - - [11/Jun/2025 02:04:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:58,544 INFO: 1******** - - [11/Jun/2025 02:04:58] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:04:58,676 INFO: 1******** - - [11/Jun/2025 02:04:58] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 02:04:58,868 INFO: 1******** - - [11/Jun/2025 02:04:58] "GET / HTTP/1.1" 200 -
2025-06-11 02:04:58,991 INFO: 1******** - - [11/Jun/2025 02:04:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:59,241 INFO: 1******** - - [11/Jun/2025 02:04:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:39,698 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:13:39,700 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:13:39,832 INFO: 1******** - - [11/Jun/2025 02:13:39] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 02:13:40,089 INFO: 1******** - - [11/Jun/2025 02:13:40] "GET /gate HTTP/1.1" 200 -
2025-06-11 02:13:40,216 INFO: 1******** - - [11/Jun/2025 02:13:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:40,441 INFO: 1******** - - [11/Jun/2025 02:13:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:40,441 INFO: 1******** - - [11/Jun/2025 02:13:40] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:40,468 INFO: 1******** - - [11/Jun/2025 02:13:40] "POST /registerf HTTP/1.1" 200 -
2025-06-11 02:13:40,797 INFO: 1******** - - [11/Jun/2025 02:13:40] "GET / HTTP/1.1" 200 -
2025-06-11 02:13:40,804 INFO: 1******** - - [11/Jun/2025 02:13:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:13:41,152 INFO: 1******** - - [11/Jun/2025 02:13:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:41,152 INFO: 1******** - - [11/Jun/2025 02:13:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:41,414 INFO: 1******** - - [11/Jun/2025 02:13:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:13:43,659 INFO: 1******** - - [11/Jun/2025 02:13:43] "GET /login HTTP/1.1" 200 -
2025-06-11 02:13:44,002 INFO: 1******** - - [11/Jun/2025 02:13:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:44,002 INFO: 1******** - - [11/Jun/2025 02:13:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:47,966 INFO: 1******** - - [11/Jun/2025 02:13:47] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:13:48,071 INFO: 1******** - - [11/Jun/2025 02:13:48] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:13:48,286 INFO: 1******** - - [11/Jun/2025 02:13:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:48,410 INFO: 1******** - - [11/Jun/2025 02:13:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:52,281 INFO: 1******** - - [11/Jun/2025 02:13:52] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:13:52,615 INFO: 1******** - - [11/Jun/2025 02:13:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:52,615 INFO: 1******** - - [11/Jun/2025 02:13:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:55,023 INFO: 1******** - - [11/Jun/2025 02:13:55] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:13:55,358 INFO: 1******** - - [11/Jun/2025 02:13:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:55,358 INFO: 1******** - - [11/Jun/2025 02:13:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:56,740 INFO: 1******** - - [11/Jun/2025 02:13:56] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:13:57,087 INFO: 1******** - - [11/Jun/2025 02:13:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:57,088 INFO: 1******** - - [11/Jun/2025 02:13:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:58,647 INFO: 1******** - - [11/Jun/2025 02:13:58] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:13:58,999 INFO: 1******** - - [11/Jun/2025 02:13:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:59,000 INFO: 1******** - - [11/Jun/2025 02:13:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:59,521 INFO: 1******** - - [11/Jun/2025 02:13:59] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:13:59,866 INFO: 1******** - - [11/Jun/2025 02:13:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:59,866 INFO: 1******** - - [11/Jun/2025 02:13:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:00,250 INFO: 1******** - - [11/Jun/2025 02:14:00] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 02:14:00,598 INFO: 1******** - - [11/Jun/2025 02:14:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:00,599 INFO: 1******** - - [11/Jun/2025 02:14:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:06,641 INFO: 1******** - - [11/Jun/2025 02:14:06] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 02:14:06,990 INFO: 1******** - - [11/Jun/2025 02:14:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:06,991 INFO: 1******** - - [11/Jun/2025 02:14:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:09,123 INFO: 1******** - - [11/Jun/2025 02:14:09] "GET / HTTP/1.1" 200 -
2025-06-11 02:14:09,511 INFO: 1******** - - [11/Jun/2025 02:14:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:09,511 INFO: 1******** - - [11/Jun/2025 02:14:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:10,871 INFO: 1******** - - [11/Jun/2025 02:14:10] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:14:11,206 INFO: 1******** - - [11/Jun/2025 02:14:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:11,207 INFO: 1******** - - [11/Jun/2025 02:14:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:14,597 INFO: 1******** - - [11/Jun/2025 02:14:14] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:14:14,929 INFO: 1******** - - [11/Jun/2025 02:14:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:14,929 INFO: 1******** - - [11/Jun/2025 02:14:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:20,158 INFO: 1******** - - [11/Jun/2025 02:14:20] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:14:20,418 INFO: 1******** - - [11/Jun/2025 02:14:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:20,497 INFO: 1******** - - [11/Jun/2025 02:14:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:21,400 INFO: 1******** - - [11/Jun/2025 02:14:21] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:14:21,745 INFO: 1******** - - [11/Jun/2025 02:14:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:21,746 INFO: 1******** - - [11/Jun/2025 02:14:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:16:04,218 INFO: 1******** - - [11/Jun/2025 02:16:04] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:16:04,480 INFO: 1******** - - [11/Jun/2025 02:16:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:16:04,558 INFO: 1******** - - [11/Jun/2025 02:16:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:16:04,572 INFO: 1******** - - [11/Jun/2025 02:16:04] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:19:02,370 INFO: 1******** - - [11/Jun/2025 02:19:02] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:19:02,634 INFO: 1******** - - [11/Jun/2025 02:19:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:19:02,717 INFO: 1******** - - [11/Jun/2025 02:19:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:19:48,132 INFO: 1******** - - [11/Jun/2025 02:19:48] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:19:48,396 INFO: 1******** - - [11/Jun/2025 02:19:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:19:48,476 INFO: 1******** - - [11/Jun/2025 02:19:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:19:54,273 INFO: 1******** - - [11/Jun/2025 02:19:54] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:19:54,673 INFO: 1******** - - [11/Jun/2025 02:19:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:19:54,674 INFO: 1******** - - [11/Jun/2025 02:19:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:00,154 INFO: 1******** - - [11/Jun/2025 02:20:00] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:20:00,402 INFO: 1******** - - [11/Jun/2025 02:20:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:00,479 INFO: 1******** - - [11/Jun/2025 02:20:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:00,495 INFO: 1******** - - [11/Jun/2025 02:20:00] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:20:01,677 INFO: 1******** - - [11/Jun/2025 02:20:01] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:20:01,947 INFO: 1******** - - [11/Jun/2025 02:20:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:02,017 INFO: 1******** - - [11/Jun/2025 02:20:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:02,036 INFO: 1******** - - [11/Jun/2025 02:20:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:20:04,481 INFO: 1******** - - [11/Jun/2025 02:20:04] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:20:04,739 INFO: 1******** - - [11/Jun/2025 02:20:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:04,816 INFO: 1******** - - [11/Jun/2025 02:20:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:06,097 INFO: 1******** - - [11/Jun/2025 02:20:06] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:20:06,428 INFO: 1******** - - [11/Jun/2025 02:20:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:06,428 INFO: 1******** - - [11/Jun/2025 02:20:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:09,215 INFO: 1******** - - [11/Jun/2025 02:20:09] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:20:09,479 INFO: 1******** - - [11/Jun/2025 02:20:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:09,541 INFO: 1******** - - [11/Jun/2025 02:20:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:12,638 INFO: 1******** - - [11/Jun/2025 02:20:12] "GET / HTTP/1.1" 200 -
2025-06-11 02:20:12,979 INFO: 1******** - - [11/Jun/2025 02:20:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:12,980 INFO: 1******** - - [11/Jun/2025 02:20:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:13,562 INFO: 1******** - - [11/Jun/2025 02:20:13] "GET / HTTP/1.1" 200 -
2025-06-11 02:20:13,897 INFO: 1******** - - [11/Jun/2025 02:20:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:13,898 INFO: 1******** - - [11/Jun/2025 02:20:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:16,417 INFO: 1******** - - [11/Jun/2025 02:20:16] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:20:16,745 INFO: 1******** - - [11/Jun/2025 02:20:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:16,746 INFO: 1******** - - [11/Jun/2025 02:20:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:18,613 INFO: 1******** - - [11/Jun/2025 02:20:18] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:20:18,878 INFO: 1******** - - [11/Jun/2025 02:20:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:18,943 INFO: 1******** - - [11/Jun/2025 02:20:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:21,780 INFO: 1******** - - [11/Jun/2025 02:20:21] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:20:22,146 INFO: 1******** - - [11/Jun/2025 02:20:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:22,147 INFO: 1******** - - [11/Jun/2025 02:20:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:27,849 INFO: 1******** - - [11/Jun/2025 02:20:27] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:20:28,112 INFO: 1******** - - [11/Jun/2025 02:20:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:28,203 INFO: 1******** - - [11/Jun/2025 02:20:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:32,467 INFO: 1******** - - [11/Jun/2025 02:20:32] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 02:20:32,798 INFO: 1******** - - [11/Jun/2025 02:20:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:32,799 INFO: 1******** - - [11/Jun/2025 02:20:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:34,043 INFO: 1******** - - [11/Jun/2025 02:20:34] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 02:20:34,378 INFO: 1******** - - [11/Jun/2025 02:20:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:34,379 INFO: 1******** - - [11/Jun/2025 02:20:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:34,874 INFO: 1******** - - [11/Jun/2025 02:20:34] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:20:35,202 INFO: 1******** - - [11/Jun/2025 02:20:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:35,203 INFO: 1******** - - [11/Jun/2025 02:20:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:41,464 INFO: 1******** - - [11/Jun/2025 02:20:41] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-11 02:20:41,716 INFO: 1******** - - [11/Jun/2025 02:20:41] "GET / HTTP/1.1" 200 -
2025-06-11 02:20:41,773 INFO: 1******** - - [11/Jun/2025 02:20:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:42,056 INFO: 1******** - - [11/Jun/2025 02:20:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:43,195 INFO: 1******** - - [11/Jun/2025 02:20:43] "GET /login HTTP/1.1" 200 -
2025-06-11 02:20:43,539 INFO: 1******** - - [11/Jun/2025 02:20:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:43,539 INFO: 1******** - - [11/Jun/2025 02:20:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:48,635 INFO: 1******** - - [11/Jun/2025 02:20:48] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:20:48,723 INFO: 1******** - - [11/Jun/2025 02:20:48] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:20:48,965 INFO: 1******** - - [11/Jun/2025 02:20:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:49,059 INFO: 1******** - - [11/Jun/2025 02:20:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:51,368 INFO: 1******** - - [11/Jun/2025 02:20:51] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:20:51,760 INFO: 1******** - - [11/Jun/2025 02:20:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:51,769 INFO: 1******** - - [11/Jun/2025 02:20:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:52,719 INFO: 1******** - - [11/Jun/2025 02:20:52] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:20:53,093 INFO: 1******** - - [11/Jun/2025 02:20:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:53,093 INFO: 1******** - - [11/Jun/2025 02:20:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:03,569 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:24:03,570 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:24:17,754 INFO: 1******** - - [11/Jun/2025 02:24:17] "[32mGET /admin/logs?type=login HTTP/1.1[0m" 302 -
2025-06-11 02:24:18,029 INFO: 1******** - - [11/Jun/2025 02:24:18] "GET /login HTTP/1.1" 200 -
2025-06-11 02:24:18,135 INFO: 1******** - - [11/Jun/2025 02:24:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:18,361 INFO: 1******** - - [11/Jun/2025 02:24:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:18,406 INFO: 1******** - - [11/Jun/2025 02:24:18] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:24:23,824 INFO: 1******** - - [11/Jun/2025 02:24:23] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:24:23,934 INFO: 1******** - - [11/Jun/2025 02:24:23] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:24:24,143 INFO: 1******** - - [11/Jun/2025 02:24:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:24,283 INFO: 1******** - - [11/Jun/2025 02:24:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:24,327 INFO: 1******** - - [11/Jun/2025 02:24:24] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:24:24,609 INFO: 1******** - - [11/Jun/2025 02:24:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:24,913 INFO: 1******** - - [11/Jun/2025 02:24:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:29,646 INFO: 1******** - - [11/Jun/2025 02:24:29] "GET /admin/edit_app/4 HTTP/1.1" 200 -
2025-06-11 02:24:29,883 INFO: 1******** - - [11/Jun/2025 02:24:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:30,082 INFO: 1******** - - [11/Jun/2025 02:24:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:41,052 INFO: 1******** - - [11/Jun/2025 02:24:41] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:24:41,316 INFO: 1******** - - [11/Jun/2025 02:24:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:41,393 INFO: 1******** - - [11/Jun/2025 02:24:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:45,449 INFO: 1******** - - [11/Jun/2025 02:24:45] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:24:45,781 INFO: 1******** - - [11/Jun/2025 02:24:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:45,781 INFO: 1******** - - [11/Jun/2025 02:24:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:46,790 INFO: 1******** - - [11/Jun/2025 02:24:46] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:24:47,117 INFO: 1******** - - [11/Jun/2025 02:24:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:47,118 INFO: 1******** - - [11/Jun/2025 02:24:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:48,666 INFO: 1******** - - [11/Jun/2025 02:24:48] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:24:48,912 INFO: 1******** - - [11/Jun/2025 02:24:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:49,007 INFO: 1******** - - [11/Jun/2025 02:24:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:51,010 INFO: 1******** - - [11/Jun/2025 02:24:51] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:24:51,347 INFO: 1******** - - [11/Jun/2025 02:24:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:51,348 INFO: 1******** - - [11/Jun/2025 02:24:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:47,135 INFO: 1******** - - [11/Jun/2025 02:25:47] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:25:47,395 INFO: 1******** - - [11/Jun/2025 02:25:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:47,522 INFO: 1******** - - [11/Jun/2025 02:25:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:50,414 INFO: 1******** - - [11/Jun/2025 02:25:50] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:25:50,679 INFO: 1******** - - [11/Jun/2025 02:25:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:50,755 INFO: 1******** - - [11/Jun/2025 02:25:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:51,382 INFO: 1******** - - [11/Jun/2025 02:25:51] "GET / HTTP/1.1" 200 -
2025-06-11 02:25:51,721 INFO: 1******** - - [11/Jun/2025 02:25:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:51,721 INFO: 1******** - - [11/Jun/2025 02:25:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:55,201 INFO: 1******** - - [11/Jun/2025 02:25:55] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:25:55,450 INFO: 1******** - - [11/Jun/2025 02:25:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:55,544 INFO: 1******** - - [11/Jun/2025 02:25:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:59,710 INFO: 1******** - - [11/Jun/2025 02:25:59] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:26:00,050 INFO: 1******** - - [11/Jun/2025 02:26:00] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 02:26:08,113 INFO: 1******** - - [11/Jun/2025 02:26:08] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:26:08,375 INFO: 1******** - - [11/Jun/2025 02:26:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:26:08,439 INFO: 1******** - - [11/Jun/2025 02:26:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:26:09,841 INFO: 1******** - - [11/Jun/2025 02:26:09] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:26:10,186 INFO: 1******** - - [11/Jun/2025 02:26:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:26:10,186 INFO: 1******** - - [11/Jun/2025 02:26:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:26:11,016 INFO: 1******** - - [11/Jun/2025 02:26:11] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:26:11,351 INFO: 1******** - - [11/Jun/2025 02:26:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:26:11,352 INFO: 1******** - - [11/Jun/2025 02:26:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:26:14,593 INFO: 1******** - - [11/Jun/2025 02:26:14] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:26:19,967 INFO: 1******** - - [11/Jun/2025 02:26:19] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:27:11,248 INFO: 1******** - - [11/Jun/2025 02:27:11] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:27:34,176 INFO: 1******** - - [11/Jun/2025 02:27:34] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:27:50,331 INFO: 1******** - - [11/Jun/2025 02:27:50] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:27:50,591 INFO: 1******** - - [11/Jun/2025 02:27:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:27:50,666 INFO: 1******** - - [11/Jun/2025 02:27:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:27:59,463 INFO: 1******** - - [11/Jun/2025 02:27:59] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:27:59,720 INFO: 1******** - - [11/Jun/2025 02:27:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:27:59,799 INFO: 1******** - - [11/Jun/2025 02:27:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:01,350 INFO: 1******** - - [11/Jun/2025 02:28:01] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:28:01,702 INFO: 1******** - - [11/Jun/2025 02:28:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:01,703 INFO: 1******** - - [11/Jun/2025 02:28:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:13,798 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:28:13,799 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:28:14,181 INFO: 1******** - - [11/Jun/2025 02:28:14] "[32mGET /admin/logs?type=activity HTTP/1.1[0m" 302 -
2025-06-11 02:28:14,285 INFO: 1******** - - [11/Jun/2025 02:28:14] "GET /login HTTP/1.1" 200 -
2025-06-11 02:28:14,571 INFO: 1******** - - [11/Jun/2025 02:28:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:14,647 INFO: 1******** - - [11/Jun/2025 02:28:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:14,748 INFO: 1******** - - [11/Jun/2025 02:28:14] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:28:22,432 INFO: 1******** - - [11/Jun/2025 02:28:22] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:28:22,547 INFO: 1******** - - [11/Jun/2025 02:28:22] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:28:22,742 INFO: 1******** - - [11/Jun/2025 02:28:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:22,897 INFO: 1******** - - [11/Jun/2025 02:28:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:25,437 INFO: 1******** - - [11/Jun/2025 02:28:25] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:28:25,779 INFO: 1******** - - [11/Jun/2025 02:28:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:25,780 INFO: 1******** - - [11/Jun/2025 02:28:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:26,729 INFO: 1******** - - [11/Jun/2025 02:28:26] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 02:28:27,088 INFO: 1******** - - [11/Jun/2025 02:28:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:27,088 INFO: 1******** - - [11/Jun/2025 02:28:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:34,695 INFO: 1******** - - [11/Jun/2025 02:28:34] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:28:34,957 INFO: 1******** - - [11/Jun/2025 02:28:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:35,034 INFO: 1******** - - [11/Jun/2025 02:28:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:41,874 INFO: 1******** - - [11/Jun/2025 02:28:41] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:28:42,138 INFO: 1******** - - [11/Jun/2025 02:28:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:42,215 INFO: 1******** - - [11/Jun/2025 02:28:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:48,877 INFO: 1******** - - [11/Jun/2025 02:28:48] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:28:50,187 INFO: 1******** - - [11/Jun/2025 02:28:50] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:28:50,525 INFO: 1******** - - [11/Jun/2025 02:28:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:50,526 INFO: 1******** - - [11/Jun/2025 02:28:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:53,776 INFO: 1******** - - [11/Jun/2025 02:28:53] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:28:54,033 INFO: 1******** - - [11/Jun/2025 02:28:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:54,144 INFO: 1******** - - [11/Jun/2025 02:28:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:00,235 INFO: 1******** - - [11/Jun/2025 02:29:00] "GET / HTTP/1.1" 200 -
2025-06-11 02:29:00,576 INFO: 1******** - - [11/Jun/2025 02:29:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:00,576 INFO: 1******** - - [11/Jun/2025 02:29:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:03,531 INFO: 1******** - - [11/Jun/2025 02:29:03] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 02:29:03,600 INFO: 1******** - - [11/Jun/2025 02:29:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:03,894 INFO: 1******** - - [11/Jun/2025 02:29:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:12,538 INFO: 1******** - - [11/Jun/2025 02:29:12] "POST /app/6/report HTTP/1.1" 200 -
2025-06-11 02:29:39,920 INFO: 1******** - - [11/Jun/2025 02:29:39] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:29:40,170 INFO: 1******** - - [11/Jun/2025 02:29:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:40,264 INFO: 1******** - - [11/Jun/2025 02:29:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:41,651 INFO: 1******** - - [11/Jun/2025 02:29:41] "GET /admin/edit_user/5 HTTP/1.1" 200 -
2025-06-11 02:29:41,990 INFO: 1******** - - [11/Jun/2025 02:29:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:41,990 INFO: 1******** - - [11/Jun/2025 02:29:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:45,824 INFO: 1******** - - [11/Jun/2025 02:29:45] "[32mPOST /admin/edit_user/5 HTTP/1.1[0m" 302 -
2025-06-11 02:29:45,938 INFO: 1******** - - [11/Jun/2025 02:29:45] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:29:46,136 INFO: 1******** - - [11/Jun/2025 02:29:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:46,307 INFO: 1******** - - [11/Jun/2025 02:29:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:48,930 INFO: 1******** - - [11/Jun/2025 02:29:48] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-11 02:29:49,255 INFO: 1******** - - [11/Jun/2025 02:29:49] "GET / HTTP/1.1" 200 -
2025-06-11 02:29:49,510 INFO: 1******** - - [11/Jun/2025 02:29:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:49,605 INFO: 1******** - - [11/Jun/2025 02:29:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:50,100 INFO: 1******** - - [11/Jun/2025 02:29:50] "GET /login HTTP/1.1" 200 -
2025-06-11 02:29:50,441 INFO: 1******** - - [11/Jun/2025 02:29:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:50,441 INFO: 1******** - - [11/Jun/2025 02:29:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:52,376 INFO: 1******** - - [11/Jun/2025 02:29:52] "POST /login HTTP/1.1" 200 -
2025-06-11 02:29:52,717 INFO: 1******** - - [11/Jun/2025 02:29:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:52,717 INFO: 1******** - - [11/Jun/2025 02:29:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:31:01,930 INFO: 1******** - - [11/Jun/2025 02:31:01] "POST /login HTTP/1.1" 200 -
2025-06-11 02:31:02,027 INFO: 1******** - - [11/Jun/2025 02:31:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:31:02,328 INFO: 1******** - - [11/Jun/2025 02:31:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:31:07,243 INFO: 1******** - - [11/Jun/2025 02:31:07] "POST /login HTTP/1.1" 200 -
2025-06-11 02:31:07,354 INFO: 1******** - - [11/Jun/2025 02:31:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:31:07,569 INFO: 1******** - - [11/Jun/2025 02:31:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:32:31,922 INFO: 1******** - - [11/Jun/2025 02:32:31] "POST /login HTTP/1.1" 200 -
2025-06-11 02:32:32,019 INFO: 1******** - - [11/Jun/2025 02:32:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:32:32,264 INFO: 1******** - - [11/Jun/2025 02:32:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:32:46,991 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:32:46,992 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:32:49,413 INFO: 1******** - - [11/Jun/2025 02:32:49] "POST /login HTTP/1.1" 200 -
2025-06-11 02:32:49,660 INFO: 1******** - - [11/Jun/2025 02:32:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:32:49,750 INFO: 1******** - - [11/Jun/2025 02:32:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:33:04,400 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:33:04,401 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:33:07,529 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:33:07,530 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:33:07,537 INFO:  * Restarting with stat
2025-06-11 02:33:07,916 WARNING:  * Debugger is active!
2025-06-11 02:33:07,928 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:33:10,762 INFO: 1******** - - [11/Jun/2025 02:33:10] "POST /login HTTP/1.1" 200 -
2025-06-11 02:33:10,968 INFO: 1******** - - [11/Jun/2025 02:33:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:33:11,093 INFO: 1******** - - [11/Jun/2025 02:33:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:33:22,560 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:33:22,716 INFO:  * Restarting with stat
2025-06-11 02:33:23,342 WARNING:  * Debugger is active!
2025-06-11 02:33:23,352 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:33:25,528 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:33:25,577 INFO:  * Restarting with stat
2025-06-11 02:33:25,985 WARNING:  * Debugger is active!
2025-06-11 02:33:25,996 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:34:32,432 INFO: 1******** - - [11/Jun/2025 02:34:32] "POST /login HTTP/1.1" 200 -
2025-06-11 02:34:37,395 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:34:37,469 INFO:  * Restarting with stat
2025-06-11 02:34:38,094 WARNING:  * Debugger is active!
2025-06-11 02:34:38,136 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:35:06,535 INFO: 1******** - - [11/Jun/2025 02:35:06] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:35:06,648 INFO: 1******** - - [11/Jun/2025 02:35:06] "[32mGET /publisher HTTP/1.1[0m" 302 -
2025-06-11 02:35:06,999 INFO: 1******** - - [11/Jun/2025 02:35:06] "GET / HTTP/1.1" 200 -
2025-06-11 02:35:07,616 INFO: 1******** - - [11/Jun/2025 02:35:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:35:07,947 INFO: 1******** - - [11/Jun/2025 02:35:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:35:08,477 INFO: 1******** - - [11/Jun/2025 02:35:08] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:35:11,748 INFO: 1******** - - [11/Jun/2025 02:35:11] "[32mGET /publisher HTTP/1.1[0m" 302 -
2025-06-11 02:35:12,135 INFO: 1******** - - [11/Jun/2025 02:35:12] "GET / HTTP/1.1" 200 -
2025-06-11 02:35:12,450 INFO: 1******** - - [11/Jun/2025 02:35:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:35:12,762 INFO: 1******** - - [11/Jun/2025 02:35:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:36:20,535 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:36:20,630 INFO:  * Restarting with stat
2025-06-11 02:36:21,213 WARNING:  * Debugger is active!
2025-06-11 02:36:21,253 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:36:38,015 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:36:38,094 INFO:  * Restarting with stat
2025-06-11 02:36:38,826 WARNING:  * Debugger is active!
2025-06-11 02:36:38,900 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:37:21,441 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:37:21,542 INFO:  * Restarting with stat
2025-06-11 02:37:22,228 WARNING:  * Debugger is active!
2025-06-11 02:37:22,251 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:38:46,116 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:38:46,117 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:39:07,866 INFO: 1******** - - [11/Jun/2025 02:39:07] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 02:39:07,989 INFO: 1******** - - [11/Jun/2025 02:39:07] "GET /login HTTP/1.1" 200 -
2025-06-11 02:39:08,254 INFO: 1******** - - [11/Jun/2025 02:39:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:39:08,361 INFO: 1******** - - [11/Jun/2025 02:39:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:39:20,117 INFO: 1******** - - [11/Jun/2025 02:39:20] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:39:20,201 INFO: 1******** - - [11/Jun/2025 02:39:20] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:39:20,433 INFO: 1******** - - [11/Jun/2025 02:39:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:39:20,559 INFO: 1******** - - [11/Jun/2025 02:39:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:39:24,776 ERROR: Exception on /publisher/edit_app/5 [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\views\routes.py", line 954, in publisher_edit_app
    return render_template('publisher/edit_app.html', app=app, categories=Config.CATEGORIES)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\publisher\edit_app.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 114, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\publisher\edit_app.html", line 153, in block 'content'
    {% if app.has_file() %}
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'has_file'
2025-06-11 02:39:24,797 INFO: 1******** - - [11/Jun/2025 02:39:24] "[35m[1mGET /publisher/edit_app/5 HTTP/1.1[0m" 500 -
2025-06-11 02:40:48,188 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:40:48,189 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:40:51,878 INFO: 1******** - - [11/Jun/2025 02:40:51] "[32mGET /publisher/edit_app/5 HTTP/1.1[0m" 302 -
2025-06-11 02:40:52,150 INFO: 1******** - - [11/Jun/2025 02:40:52] "GET /login HTTP/1.1" 200 -
2025-06-11 02:40:52,259 INFO: 1******** - - [11/Jun/2025 02:40:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:40:52,496 INFO: 1******** - - [11/Jun/2025 02:40:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:40:57,938 INFO: 1******** - - [11/Jun/2025 02:40:57] "POST /login HTTP/1.1" 200 -
2025-06-11 02:40:58,040 INFO: 1******** - - [11/Jun/2025 02:40:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:40:58,271 INFO: 1******** - - [11/Jun/2025 02:40:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:01,363 INFO: 1******** - - [11/Jun/2025 02:41:01] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:41:01,490 INFO: 1******** - - [11/Jun/2025 02:41:01] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:41:01,676 INFO: 1******** - - [11/Jun/2025 02:41:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:41:01,825 INFO: 1******** - - [11/Jun/2025 02:41:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:05,023 INFO: 1******** - - [11/Jun/2025 02:41:05] "GET /publisher/edit_app/5 HTTP/1.1" 200 -
2025-06-11 02:41:05,275 INFO: 1******** - - [11/Jun/2025 02:41:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:41:05,401 INFO: 1******** - - [11/Jun/2025 02:41:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:14,665 INFO: 1******** - - [11/Jun/2025 02:41:14] "[32mGET /app/5 HTTP/1.1[0m" 302 -
2025-06-11 02:41:14,930 INFO: 1******** - - [11/Jun/2025 02:41:14] "GET / HTTP/1.1" 200 -
2025-06-11 02:41:14,977 INFO: 1******** - - [11/Jun/2025 02:41:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:15,284 INFO: 1******** - - [11/Jun/2025 02:41:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:08,800 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:42:08,801 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:42:28,376 INFO: 1******** - - [11/Jun/2025 02:42:28] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 02:42:28,590 INFO: 1******** - - [11/Jun/2025 02:42:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:28,705 INFO: 1******** - - [11/Jun/2025 02:42:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:41,370 INFO: 1******** - - [11/Jun/2025 02:42:41] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 02:42:41,635 INFO: 1******** - - [11/Jun/2025 02:42:41] "GET /login HTTP/1.1" 200 -
2025-06-11 02:42:41,681 INFO: 1******** - - [11/Jun/2025 02:42:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:41,976 INFO: 1******** - - [11/Jun/2025 02:42:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:52,719 INFO: 1******** - - [11/Jun/2025 02:42:52] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:42:52,793 INFO: 1******** - - [11/Jun/2025 02:42:52] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:42:53,040 INFO: 1******** - - [11/Jun/2025 02:42:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:53,150 INFO: 1******** - - [11/Jun/2025 02:42:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:56,452 INFO: 1******** - - [11/Jun/2025 02:42:56] "GET /publisher/edit_app/5 HTTP/1.1" 200 -
2025-06-11 02:42:56,707 INFO: 1******** - - [11/Jun/2025 02:42:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:56,784 INFO: 1******** - - [11/Jun/2025 02:42:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:59,740 INFO: 1******** - - [11/Jun/2025 02:42:59] "GET /app/5 HTTP/1.1" 200 -
2025-06-11 02:42:59,978 INFO: 1******** - - [11/Jun/2025 02:42:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:00,086 INFO: 1******** - - [11/Jun/2025 02:43:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:11,792 INFO: 1******** - - [11/Jun/2025 02:43:11] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:43:12,052 INFO: 1******** - - [11/Jun/2025 02:43:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:12,159 INFO: 1******** - - [11/Jun/2025 02:43:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:13,733 INFO: 1******** - - [11/Jun/2025 02:43:13] "GET /publisher/edit_app/5 HTTP/1.1" 200 -
2025-06-11 02:43:14,069 INFO: 1******** - - [11/Jun/2025 02:43:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:14,070 INFO: 1******** - - [11/Jun/2025 02:43:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:21,384 INFO: 1******** - - [11/Jun/2025 02:43:21] "GET / HTTP/1.1" 200 -
2025-06-11 02:43:21,614 INFO: 1******** - - [11/Jun/2025 02:43:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:21,737 INFO: 1******** - - [11/Jun/2025 02:43:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:22,116 INFO: 1******** - - [11/Jun/2025 02:43:22] "GET / HTTP/1.1" 200 -
2025-06-11 02:43:22,452 INFO: 1******** - - [11/Jun/2025 02:43:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:22,453 INFO: 1******** - - [11/Jun/2025 02:43:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:25,148 INFO: 1******** - - [11/Jun/2025 02:43:25] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 02:43:25,408 INFO: 1******** - - [11/Jun/2025 02:43:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:25,500 INFO: 1******** - - [11/Jun/2025 02:43:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:26,323 INFO: 1******** - - [11/Jun/2025 02:43:26] "[32mGET /download/1 HTTP/1.1[0m" 302 -
2025-06-11 02:43:30,592 INFO: 1******** - - [11/Jun/2025 02:43:30] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 02:43:30,833 INFO: 1******** - - [11/Jun/2025 02:43:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:30,942 INFO: 1******** - - [11/Jun/2025 02:43:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:32,782 INFO: 1******** - - [11/Jun/2025 02:43:32] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:43:33,120 INFO: 1******** - - [11/Jun/2025 02:43:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:33,121 INFO: 1******** - - [11/Jun/2025 02:43:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:34,662 INFO: 1******** - - [11/Jun/2025 02:43:34] "GET /publisher/add_app HTTP/1.1" 200 -
2025-06-11 02:43:34,922 INFO: 1******** - - [11/Jun/2025 02:43:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:34,999 INFO: 1******** - - [11/Jun/2025 02:43:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:50:05,075 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 02:50:05,103 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:50:25,169 INFO: 1******** - - [11/Jun/2025 02:50:25] "[32mGET /admin/add_app HTTP/1.1[0m" 302 -
2025-06-11 02:50:25,437 INFO: 1******** - - [11/Jun/2025 02:50:25] "GET /login HTTP/1.1" 200 -
2025-06-11 02:50:25,598 INFO: 1******** - - [11/Jun/2025 02:50:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:50:25,809 INFO: 1******** - - [11/Jun/2025 02:50:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:54:35,077 INFO: 1******** - - [11/Jun/2025 02:54:35] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:54:35,427 INFO: 1******** - - [11/Jun/2025 02:54:35] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:54:35,670 INFO: 1******** - - [11/Jun/2025 02:54:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:54:35,807 INFO: 1******** - - [11/Jun/2025 02:54:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:54:38,343 INFO: 1******** - - [11/Jun/2025 02:54:38] "GET /publisher/add_app HTTP/1.1" 200 -
2025-06-11 02:54:38,693 INFO: 1******** - - [11/Jun/2025 02:54:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:54:38,694 INFO: 1******** - - [11/Jun/2025 02:54:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:12,320 INFO: 1******** - - [11/Jun/2025 02:55:12] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-11 02:55:12,651 INFO: 1******** - - [11/Jun/2025 02:55:12] "GET / HTTP/1.1" 200 -
2025-06-11 02:55:17,691 INFO: 1******** - - [11/Jun/2025 02:55:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:18,007 INFO: 1******** - - [11/Jun/2025 02:55:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:55:20,766 INFO: 1******** - - [11/Jun/2025 02:55:20] "GET /login HTTP/1.1" 200 -
2025-06-11 02:55:21,024 INFO: 1******** - - [11/Jun/2025 02:55:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:55:21,298 INFO: 1******** - - [11/Jun/2025 02:55:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:29,524 INFO: 1******** - - [11/Jun/2025 02:55:29] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:55:30,815 INFO: 1******** - - [11/Jun/2025 02:55:30] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:55:32,749 INFO: 1******** - - [11/Jun/2025 02:55:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:33,076 INFO: 1******** - - [11/Jun/2025 02:55:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:55:38,651 INFO: 1******** - - [11/Jun/2025 02:55:38] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:55:44,971 INFO: 1******** - - [11/Jun/2025 02:55:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:45,367 INFO: 1******** - - [11/Jun/2025 02:55:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:01:41,358 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:01:41,360 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:02:01,755 INFO: 1******** - - [11/Jun/2025 03:02:01] "[32mGET /publisher/add_app HTTP/1.1[0m" 302 -
2025-06-11 03:02:02,123 INFO: 1******** - - [11/Jun/2025 03:02:02] "GET /login HTTP/1.1" 200 -
2025-06-11 03:02:03,196 INFO: 1******** - - [11/Jun/2025 03:02:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:02:03,330 INFO: 1******** - - [11/Jun/2025 03:02:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:02:17,925 INFO: 1******** - - [11/Jun/2025 03:02:17] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 03:02:17,983 INFO: 1******** - - [11/Jun/2025 03:02:17] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:02:18,412 INFO: 1******** - - [11/Jun/2025 03:02:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:02:18,413 INFO: 1******** - - [11/Jun/2025 03:02:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:02:23,391 INFO: 1******** - - [11/Jun/2025 03:02:23] "GET /publisher/add_app HTTP/1.1" 200 -
2025-06-11 03:02:23,817 INFO: 1******** - - [11/Jun/2025 03:02:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:02:23,818 INFO: 1******** - - [11/Jun/2025 03:02:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:03:05,187 INFO: 1******** - - [11/Jun/2025 03:03:05] "[32mPOST /publisher/add_app HTTP/1.1[0m" 302 -
2025-06-11 03:03:05,578 INFO: 1******** - - [11/Jun/2025 03:03:05] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:03:05,838 INFO: 1******** - - [11/Jun/2025 03:03:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:03:05,965 INFO: 1******** - - [11/Jun/2025 03:03:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:03:08,330 INFO: 1******** - - [11/Jun/2025 03:03:08] "[32mGET /app/9 HTTP/1.1[0m" 302 -
2025-06-11 03:03:08,697 INFO: 1******** - - [11/Jun/2025 03:03:08] "GET / HTTP/1.1" 200 -
2025-06-11 03:03:08,932 INFO: 1******** - - [11/Jun/2025 03:03:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:03:09,080 INFO: 1******** - - [11/Jun/2025 03:03:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:05:38,593 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:05:38,595 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:06:01,819 INFO: 1******** - - [11/Jun/2025 03:06:01] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 03:06:02,040 INFO: 1******** - - [11/Jun/2025 03:06:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:02,207 INFO: 1******** - - [11/Jun/2025 03:06:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:05,348 INFO: 1******** - - [11/Jun/2025 03:06:05] "GET / HTTP/1.1" 200 -
2025-06-11 03:06:05,558 INFO: 1******** - - [11/Jun/2025 03:06:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:05,718 INFO: 1******** - - [11/Jun/2025 03:06:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:05,720 INFO: 1******** - - [11/Jun/2025 03:06:05] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:09,108 INFO: 1******** - - [11/Jun/2025 03:06:09] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 03:06:09,464 INFO: 1******** - - [11/Jun/2025 03:06:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:09,474 INFO: 1******** - - [11/Jun/2025 03:06:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:09,476 INFO: 1******** - - [11/Jun/2025 03:06:09] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:17,166 INFO: 1******** - - [11/Jun/2025 03:06:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:20,037 INFO: 1******** - - [11/Jun/2025 03:06:20] "GET / HTTP/1.1" 200 -
2025-06-11 03:06:20,278 INFO: 1******** - - [11/Jun/2025 03:06:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:20,408 INFO: 1******** - - [11/Jun/2025 03:06:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:20,410 INFO: 1******** - - [11/Jun/2025 03:06:20] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:25,272 INFO: 1******** - - [11/Jun/2025 03:06:25] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 03:06:25,644 INFO: 1******** - - [11/Jun/2025 03:06:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:25,645 INFO: 1******** - - [11/Jun/2025 03:06:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:25,646 INFO: 1******** - - [11/Jun/2025 03:06:25] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:33,877 INFO: 1******** - - [11/Jun/2025 03:06:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:11:12,315 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:11:12,316 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:11:32,049 INFO: 1******** - - [11/Jun/2025 03:11:32] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 03:11:32,347 INFO: 1******** - - [11/Jun/2025 03:11:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:11:32,415 INFO: 1******** - - [11/Jun/2025 03:11:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:11:39,340 INFO: 1******** - - [11/Jun/2025 03:11:39] "GET / HTTP/1.1" 200 -
2025-06-11 03:11:39,696 INFO: 1******** - - [11/Jun/2025 03:11:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:11:39,697 INFO: 1******** - - [11/Jun/2025 03:11:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:11:39,699 INFO: 1******** - - [11/Jun/2025 03:11:39] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:11:43,256 INFO: 1******** - - [11/Jun/2025 03:11:43] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 03:11:43,619 INFO: 1******** - - [11/Jun/2025 03:11:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:11:43,620 INFO: 1******** - - [11/Jun/2025 03:11:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:11:47,237 INFO: 1******** - - [11/Jun/2025 03:11:47] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 03:11:47,600 INFO: 1******** - - [11/Jun/2025 03:11:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:11:47,601 INFO: 1******** - - [11/Jun/2025 03:11:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:11:47,604 INFO: 1******** - - [11/Jun/2025 03:11:47] "[33mGET /uploads/screenshots\\20250611_030305_01e2d015-e7ab-4fae-b174-513679ebe221.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:11:47,605 INFO: 1******** - - [11/Jun/2025 03:11:47] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:11:53,633 INFO: 1******** - - [11/Jun/2025 03:11:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:11:59,811 INFO: 1******** - - [11/Jun/2025 03:11:59] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:12:06,771 INFO: 1******** - - [11/Jun/2025 03:12:06] "[33mGET /uploads/icons/C20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:12:09,444 INFO: 1******** - - [11/Jun/2025 03:12:09] "[33mGET /uploads/icons/20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:15:33,989 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:15:33,990 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:16:07,931 INFO: 1******** - - [11/Jun/2025 03:16:07] "GET / HTTP/1.1" 200 -
2025-06-11 03:16:08,246 INFO: 1******** - - [11/Jun/2025 03:16:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:16:08,299 INFO: 1******** - - [11/Jun/2025 03:16:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:16:08,302 INFO: 1******** - - [11/Jun/2025 03:16:08] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:16:08,439 INFO: 1******** - - [11/Jun/2025 03:16:08] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:17:07,996 INFO: 1******** - - [11/Jun/2025 03:17:07] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 03:17:08,163 INFO: 1******** - - [11/Jun/2025 03:17:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:17:08,349 INFO: 1******** - - [11/Jun/2025 03:17:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:17:08,367 INFO: 1******** - - [11/Jun/2025 03:17:08] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:17:08,395 INFO: 1******** - - [11/Jun/2025 03:17:08] "[33mGET /uploads/screenshots\\20250611_030305_01e2d015-e7ab-4fae-b174-513679ebe221.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:17:30,700 INFO: 1******** - - [11/Jun/2025 03:17:30] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:17:35,048 INFO: 1******** - - [11/Jun/2025 03:17:35] "[33mGET /uploads/icons/C20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:17:37,108 INFO: 1******** - - [11/Jun/2025 03:17:37] "[33mGET /uploads/icons/20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:17:42,056 INFO: 1******** - - [11/Jun/2025 03:17:42] "GET /login HTTP/1.1" 200 -
2025-06-11 03:17:42,319 INFO: 1******** - - [11/Jun/2025 03:17:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:17:42,397 INFO: 1******** - - [11/Jun/2025 03:17:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:17:47,566 INFO: 1******** - - [11/Jun/2025 03:17:47] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 03:17:47,908 INFO: 1******** - - [11/Jun/2025 03:17:47] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:17:48,150 INFO: 1******** - - [11/Jun/2025 03:17:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:17:48,260 INFO: 1******** - - [11/Jun/2025 03:17:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:17:50,531 INFO: 1******** - - [11/Jun/2025 03:17:50] "GET /publisher/add_app HTTP/1.1" 200 -
2025-06-11 03:17:50,876 INFO: 1******** - - [11/Jun/2025 03:17:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:17:50,877 INFO: 1******** - - [11/Jun/2025 03:17:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:18:20,079 INFO: 1******** - - [11/Jun/2025 03:18:20] "[32mPOST /publisher/add_app HTTP/1.1[0m" 302 -
2025-06-11 03:18:20,401 INFO: 1******** - - [11/Jun/2025 03:18:20] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:18:20,659 INFO: 1******** - - [11/Jun/2025 03:18:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:18:20,739 INFO: 1******** - - [11/Jun/2025 03:18:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:18:23,480 ERROR: Exception on /publisher/edit_app/10 [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\views\routes.py", line 1132, in publisher_edit_app
    return render_template('publisher/edit_app.html', app=app, categories=Config.CATEGORIES)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\publisher\edit_app.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 114, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\publisher\edit_app.html", line 100, in block 'content'
    <img src="{{ url_for('uploaded_file', filename=app.icon_path) }}"
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1121, in url_for
    return self.handle_url_build_error(error, endpoint, values)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1110, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
        endpoint,
    ...<3 lines>...
        force_external=_external,
    )
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'uploaded_file' with values ['filename']. Did you mean 'views.uploaded_file' instead?
2025-06-11 03:18:23,503 INFO: 1******** - - [11/Jun/2025 03:18:23] "[35m[1mGET /publisher/edit_app/10 HTTP/1.1[0m" 500 -
2025-06-11 03:18:26,094 INFO: 1******** - - [11/Jun/2025 03:18:26] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:18:27,087 INFO: 1******** - - [11/Jun/2025 03:18:27] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 03:18:27,434 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:27,434 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:27,436 INFO: 1******** - - [11/Jun/2025 03:18:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:18:27,437 INFO: 1******** - - [11/Jun/2025 03:18:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:18:27,693 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:27,755 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:27,769 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:27,772 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:27,772 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:27,773 INFO: 1******** - - [11/Jun/2025 03:18:27] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:28,019 INFO: 1******** - - [11/Jun/2025 03:18:28] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 03:18:32,854 INFO: 1******** - - [11/Jun/2025 03:18:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:18:45,396 INFO: 1******** - - [11/Jun/2025 03:18:45] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:20:10,224 INFO: 1******** - - [11/Jun/2025 03:20:10] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:20:38,271 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:20:38,272 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:20:38,286 INFO:  * Restarting with stat
2025-06-11 03:20:39,108 WARNING:  * Debugger is active!
2025-06-11 03:20:39,135 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:20:39,311 INFO: 1******** - - [11/Jun/2025 03:20:39] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:20:45,807 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 03:20:45,963 INFO:  * Restarting with stat
2025-06-11 03:20:46,931 WARNING:  * Debugger is active!
2025-06-11 03:20:46,951 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:20:47,054 INFO: 1******** - - [11/Jun/2025 03:20:47] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:20:57,848 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 03:20:57,976 INFO:  * Restarting with stat
2025-06-11 03:20:59,228 WARNING:  * Debugger is active!
2025-06-11 03:20:59,253 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:21:00,486 INFO: 1******** - - [11/Jun/2025 03:21:00] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:21:00,992 INFO: 1******** - - [11/Jun/2025 03:21:00] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:21:06,964 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 03:21:07,148 INFO:  * Restarting with stat
2025-06-11 03:21:08,477 WARNING:  * Debugger is active!
2025-06-11 03:21:08,512 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:22:33,892 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:22:33,987 INFO:  * Restarting with stat
2025-06-11 03:22:34,860 WARNING:  * Debugger is active!
2025-06-11 03:22:34,883 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:22:45,821 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:22:46,007 INFO:  * Restarting with stat
2025-06-11 03:22:47,375 WARNING:  * Debugger is active!
2025-06-11 03:22:47,401 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:22:54,155 INFO: 1******** - - [11/Jun/2025 03:22:54] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:22:55,608 INFO: 1******** - - [11/Jun/2025 03:22:55] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:24:19,088 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:24:19,090 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:24:37,980 INFO: 1******** - - [11/Jun/2025 03:24:37] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:24:45,885 INFO: 1******** - - [11/Jun/2025 03:24:45] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 03:24:46,431 INFO: 1******** - - [11/Jun/2025 03:24:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:24:46,433 INFO: 1******** - - [11/Jun/2025 03:24:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:28:24,270 INFO: 1******** - - [11/Jun/2025 03:28:24] "GET / HTTP/1.1" 200 -
2025-06-11 03:28:24,494 INFO: 1******** - - [11/Jun/2025 03:28:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:28:24,656 INFO: 1******** - - [11/Jun/2025 03:28:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:28:24,657 INFO: 1******** - - [11/Jun/2025 03:28:24] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:24,659 INFO: 1******** - - [11/Jun/2025 03:28:24] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:28:26,004 INFO: 1******** - - [11/Jun/2025 03:28:26] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:27,334 INFO: 1******** - - [11/Jun/2025 03:28:27] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:29,665 INFO: 1******** - - [11/Jun/2025 03:28:29] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 03:28:30,026 INFO: 1******** - - [11/Jun/2025 03:28:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:28:30,027 INFO: 1******** - - [11/Jun/2025 03:28:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:28:30,028 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:30,029 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:30,286 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:30,375 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:30,376 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:30,377 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:30,378 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:30,392 INFO: 1******** - - [11/Jun/2025 03:28:30] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:34,537 INFO: 1******** - - [11/Jun/2025 03:28:34] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:28:42,811 INFO: 1******** - - [11/Jun/2025 03:28:42] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:29:11,947 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:29:12,381 INFO:  * Restarting with stat
2025-06-11 03:29:13,538 WARNING:  * Debugger is active!
2025-06-11 03:29:13,571 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:29:38,907 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:29:38,908 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:29:43,731 INFO: 1******** - - [11/Jun/2025 03:29:43] "GET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1" 200 -
2025-06-11 03:30:01,968 INFO: 1******** - - [11/Jun/2025 03:30:01] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 03:30:02,323 INFO: 1******** - - [11/Jun/2025 03:30:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:30:02,325 INFO: 1******** - - [11/Jun/2025 03:30:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:30:02,328 INFO: 1******** - - [11/Jun/2025 03:30:02] "[36mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:02,331 INFO: 1******** - - [11/Jun/2025 03:30:02] "GET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1" 200 -
2025-06-11 03:30:02,584 INFO: 1******** - - [11/Jun/2025 03:30:02] "GET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1" 200 -
2025-06-11 03:30:02,671 INFO: 1******** - - [11/Jun/2025 03:30:02] "GET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1" 200 -
2025-06-11 03:30:02,671 INFO: 1******** - - [11/Jun/2025 03:30:02] "GET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1" 200 -
2025-06-11 03:30:02,672 INFO: 1******** - - [11/Jun/2025 03:30:02] "GET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1" 200 -
2025-06-11 03:30:02,674 INFO: 1******** - - [11/Jun/2025 03:30:02] "GET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1" 200 -
2025-06-11 03:30:02,677 INFO: 1******** - - [11/Jun/2025 03:30:02] "GET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1" 200 -
2025-06-11 03:30:03,094 INFO: 1******** - - [11/Jun/2025 03:30:03] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:30:18,039 INFO: 1******** - - [11/Jun/2025 03:30:18] "[32mGET /download/10 HTTP/1.1[0m" 302 -
2025-06-11 03:30:18,372 INFO: 1******** - - [11/Jun/2025 03:30:18] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 03:30:18,612 INFO: 1******** - - [11/Jun/2025 03:30:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:30:18,740 INFO: 1******** - - [11/Jun/2025 03:30:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:30:18,744 INFO: 1******** - - [11/Jun/2025 03:30:18] "[36mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:18,747 INFO: 1******** - - [11/Jun/2025 03:30:18] "[36mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:18,777 INFO: 1******** - - [11/Jun/2025 03:30:18] "[36mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:19,100 INFO: 1******** - - [11/Jun/2025 03:30:19] "[36mGET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:19,104 INFO: 1******** - - [11/Jun/2025 03:30:19] "[36mGET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:19,105 INFO: 1******** - - [11/Jun/2025 03:30:19] "[36mGET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:19,106 INFO: 1******** - - [11/Jun/2025 03:30:19] "[36mGET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:19,111 INFO: 1******** - - [11/Jun/2025 03:30:19] "[36mGET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1[0m" 304 -
2025-06-11 03:30:19,241 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:30:19,395 INFO:  * Restarting with stat
2025-06-11 03:30:20,808 WARNING:  * Debugger is active!
2025-06-11 03:30:20,832 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:30:44,792 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:30:44,794 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:30:57,648 INFO: 1******** - - [11/Jun/2025 03:30:57] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:11,196 INFO: 1******** - - [11/Jun/2025 03:31:11] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 03:31:11,568 INFO: 1******** - - [11/Jun/2025 03:31:11] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:11,572 INFO: 1******** - - [11/Jun/2025 03:31:11] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:11,762 INFO: 1******** - - [11/Jun/2025 03:31:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:11,762 INFO: 1******** - - [11/Jun/2025 03:31:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:11,822 INFO: 1******** - - [11/Jun/2025 03:31:11] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:11,907 INFO: 1******** - - [11/Jun/2025 03:31:11] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:11,908 INFO: 1******** - - [11/Jun/2025 03:31:11] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:12,110 INFO: 1******** - - [11/Jun/2025 03:31:12] "[33mGET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:12,114 INFO: 1******** - - [11/Jun/2025 03:31:12] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:12,125 INFO: 1******** - - [11/Jun/2025 03:31:12] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:12,145 INFO: 1******** - - [11/Jun/2025 03:31:12] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:12,477 INFO: 1******** - - [11/Jun/2025 03:31:12] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:31:13,199 INFO: 1******** - - [11/Jun/2025 03:31:13] "GET / HTTP/1.1" 200 -
2025-06-11 03:31:13,564 INFO: 1******** - - [11/Jun/2025 03:31:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:13,564 INFO: 1******** - - [11/Jun/2025 03:31:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:13,586 INFO: 1******** - - [11/Jun/2025 03:31:13] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:31:13,592 INFO: 1******** - - [11/Jun/2025 03:31:13] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:14,028 INFO: 1******** - - [11/Jun/2025 03:31:14] "GET /login HTTP/1.1" 200 -
2025-06-11 03:31:14,378 INFO: 1******** - - [11/Jun/2025 03:31:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:14,379 INFO: 1******** - - [11/Jun/2025 03:31:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:17,628 INFO: 1******** - - [11/Jun/2025 03:31:17] "GET / HTTP/1.1" 200 -
2025-06-11 03:31:17,980 INFO: 1******** - - [11/Jun/2025 03:31:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:17,980 INFO: 1******** - - [11/Jun/2025 03:31:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:17,996 INFO: 1******** - - [11/Jun/2025 03:31:17] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:31:17,997 INFO: 1******** - - [11/Jun/2025 03:31:17] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:21,212 INFO: 1******** - - [11/Jun/2025 03:31:21] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 03:31:21,562 INFO: 1******** - - [11/Jun/2025 03:31:21] "GET /admin HTTP/1.1" 200 -
2025-06-11 03:31:21,798 INFO: 1******** - - [11/Jun/2025 03:31:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:21,911 INFO: 1******** - - [11/Jun/2025 03:31:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:23,119 INFO: 1******** - - [11/Jun/2025 03:31:23] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 03:31:23,478 INFO: 1******** - - [11/Jun/2025 03:31:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:23,479 INFO: 1******** - - [11/Jun/2025 03:31:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:47,426 INFO: 1******** - - [11/Jun/2025 03:31:47] "[32mPOST /admin/add_app HTTP/1.1[0m" 302 -
2025-06-11 03:31:47,877 INFO: 1******** - - [11/Jun/2025 03:31:47] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 03:31:48,090 INFO: 1******** - - [11/Jun/2025 03:31:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:48,234 INFO: 1******** - - [11/Jun/2025 03:31:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:48,251 INFO: 1******** - - [11/Jun/2025 03:31:48] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:31:48,252 INFO: 1******** - - [11/Jun/2025 03:31:48] "GET /uploads/icons/20250611_033145_black.webp HTTP/1.1" 200 -
2025-06-11 03:31:48,281 INFO: 1******** - - [11/Jun/2025 03:31:48] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:31:50,649 INFO: 1******** - - [11/Jun/2025 03:31:50] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:31:50,896 INFO: 1******** - - [11/Jun/2025 03:31:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:51,009 INFO: 1******** - - [11/Jun/2025 03:31:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:51,028 INFO: 1******** - - [11/Jun/2025 03:31:51] "GET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1" 200 -
2025-06-11 03:31:51,033 INFO: 1******** - - [11/Jun/2025 03:31:51] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:31:51,046 INFO: 1******** - - [11/Jun/2025 03:31:51] "GET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1" 200 -
2025-06-11 03:31:51,357 INFO: 1******** - - [11/Jun/2025 03:31:51] "GET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1" 200 -
2025-06-11 03:31:53,443 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:31:53,585 INFO:  * Restarting with stat
2025-06-11 03:31:54,938 WARNING:  * Debugger is active!
2025-06-11 03:31:54,959 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:31:55,419 INFO: 1******** - - [11/Jun/2025 03:31:55] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:31:55,901 INFO: 1******** - - [11/Jun/2025 03:31:55] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:31:56,123 INFO: 1******** - - [11/Jun/2025 03:31:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:31:56,285 INFO: 1******** - - [11/Jun/2025 03:31:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:31:56,299 INFO: 1******** - - [11/Jun/2025 03:31:56] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:31:56,303 INFO: 1******** - - [11/Jun/2025 03:31:56] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:31:56,625 INFO: 1******** - - [11/Jun/2025 03:31:56] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:31:56,628 INFO: 1******** - - [11/Jun/2025 03:31:56] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:16,437 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:32:16,438 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:32:26,498 INFO: 1******** - - [11/Jun/2025 03:32:26] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:32:26,764 INFO: 1******** - - [11/Jun/2025 03:32:26] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:32:26,816 INFO: 1******** - - [11/Jun/2025 03:32:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:32:27,123 INFO: 1******** - - [11/Jun/2025 03:32:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:32:27,144 INFO: 1******** - - [11/Jun/2025 03:32:27] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:27,147 INFO: 1******** - - [11/Jun/2025 03:32:27] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:27,166 INFO: 1******** - - [11/Jun/2025 03:32:27] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:27,468 INFO: 1******** - - [11/Jun/2025 03:32:27] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:30,385 INFO: 1******** - - [11/Jun/2025 03:32:30] "GET /test-upload-route HTTP/1.1" 200 -
2025-06-11 03:32:46,676 INFO: 1******** - - [11/Jun/2025 03:32:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:32:49,847 INFO: 1******** - - [11/Jun/2025 03:32:49] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:32:50,121 INFO: 1******** - - [11/Jun/2025 03:32:50] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:32:50,224 INFO: 1******** - - [11/Jun/2025 03:32:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:32:50,534 INFO: 1******** - - [11/Jun/2025 03:32:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:32:50,554 INFO: 1******** - - [11/Jun/2025 03:32:50] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:50,556 INFO: 1******** - - [11/Jun/2025 03:32:50] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:50,875 INFO: 1******** - - [11/Jun/2025 03:32:50] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:50,885 INFO: 1******** - - [11/Jun/2025 03:32:50] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:32:56,673 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 03:32:56,763 INFO:  * Restarting with stat
2025-06-11 03:32:57,898 WARNING:  * Debugger is active!
2025-06-11 03:32:57,930 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:33:11,284 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 03:33:11,363 INFO:  * Restarting with stat
2025-06-11 03:33:12,278 WARNING:  * Debugger is active!
2025-06-11 03:33:12,307 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:33:21,318 INFO: 1******** - - [11/Jun/2025 03:33:21] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:33:21,731 INFO: 1******** - - [11/Jun/2025 03:33:21] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:33:21,989 INFO: 1******** - - [11/Jun/2025 03:33:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:33:22,140 INFO: 1******** - - [11/Jun/2025 03:33:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:33:22,157 INFO: 1******** - - [11/Jun/2025 03:33:22] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:22,160 INFO: 1******** - - [11/Jun/2025 03:33:22] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:22,494 INFO: 1******** - - [11/Jun/2025 03:33:22] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:22,497 INFO: 1******** - - [11/Jun/2025 03:33:22] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:32,097 INFO: 1******** - - [11/Jun/2025 03:33:32] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:33:32,332 INFO: 1******** - - [11/Jun/2025 03:33:32] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:33:32,494 INFO: 1******** - - [11/Jun/2025 03:33:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:33:32,806 INFO: 1******** - - [11/Jun/2025 03:33:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:33:32,915 INFO: 1******** - - [11/Jun/2025 03:33:32] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:32,921 INFO: 1******** - - [11/Jun/2025 03:33:32] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:33,196 INFO: 1******** - - [11/Jun/2025 03:33:33] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:33,212 INFO: 1******** - - [11/Jun/2025 03:33:33] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:37,895 INFO: 1******** - - [11/Jun/2025 03:33:37] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:33:38,097 INFO: 1******** - - [11/Jun/2025 03:33:38] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:33:38,250 INFO: 1******** - - [11/Jun/2025 03:33:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:33:38,472 INFO: 1******** - - [11/Jun/2025 03:33:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:33:38,489 INFO: 1******** - - [11/Jun/2025 03:33:38] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:38,500 INFO: 1******** - - [11/Jun/2025 03:33:38] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:38,514 INFO: 1******** - - [11/Jun/2025 03:33:38] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:38,821 INFO: 1******** - - [11/Jun/2025 03:33:38] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:40,760 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:33:40,761 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:33:43,716 INFO: 1******** - - [11/Jun/2025 03:33:43] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:33:44,084 INFO: 1******** - - [11/Jun/2025 03:33:44] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:33:44,305 INFO: 1******** - - [11/Jun/2025 03:33:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:33:44,451 INFO: 1******** - - [11/Jun/2025 03:33:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:33:44,479 INFO: 1******** - - [11/Jun/2025 03:33:44] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:44,483 INFO: 1******** - - [11/Jun/2025 03:33:44] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:44,813 INFO: 1******** - - [11/Jun/2025 03:33:44] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:44,884 INFO: 1******** - - [11/Jun/2025 03:33:44] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:33:52,118 INFO: 1******** - - [11/Jun/2025 03:33:52] "GET /test-upload-route HTTP/1.1" 200 -
2025-06-11 03:34:17,316 INFO: 1******** - - [11/Jun/2025 03:34:17] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:34:17,646 INFO: 1******** - - [11/Jun/2025 03:34:17] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:34:17,895 INFO: 1******** - - [11/Jun/2025 03:34:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:34:18,009 INFO: 1******** - - [11/Jun/2025 03:34:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:34:18,015 INFO: 1******** - - [11/Jun/2025 03:34:18] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:34:18,018 INFO: 1******** - - [11/Jun/2025 03:34:18] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:34:18,045 INFO: 1******** - - [11/Jun/2025 03:34:18] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:34:18,337 INFO: 1******** - - [11/Jun/2025 03:34:18] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:02,074 INFO: 1******** - - [11/Jun/2025 03:38:02] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:38:02,326 INFO: 1******** - - [11/Jun/2025 03:38:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:02,441 INFO: 1******** - - [11/Jun/2025 03:38:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:02,450 INFO: 1******** - - [11/Jun/2025 03:38:02] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:02,452 INFO: 1******** - - [11/Jun/2025 03:38:02] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:02,476 INFO: 1******** - - [11/Jun/2025 03:38:02] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:02,780 INFO: 1******** - - [11/Jun/2025 03:38:02] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:03,134 INFO: 1******** - - [11/Jun/2025 03:38:03] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:38:03,422 INFO: 1******** - - [11/Jun/2025 03:38:03] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:38:03,555 INFO: 1******** - - [11/Jun/2025 03:38:03] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:38:03,743 INFO: 1******** - - [11/Jun/2025 03:38:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:03,905 INFO: 1******** - - [11/Jun/2025 03:38:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:03,909 INFO: 1******** - - [11/Jun/2025 03:38:03] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:03,915 INFO: 1******** - - [11/Jun/2025 03:38:03] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:03,942 INFO: 1******** - - [11/Jun/2025 03:38:03] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:04,289 INFO: 1******** - - [11/Jun/2025 03:38:04] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:10,116 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:38:10,117 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:38:10,130 INFO:  * Restarting with stat
2025-06-11 03:38:10,950 WARNING:  * Debugger is active!
2025-06-11 03:38:10,972 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:38:11,203 INFO: 1******** - - [11/Jun/2025 03:38:11] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:38:11,366 INFO: 1******** - - [11/Jun/2025 03:38:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:11,554 INFO: 1******** - - [11/Jun/2025 03:38:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:11,556 INFO: 1******** - - [11/Jun/2025 03:38:11] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:11,559 INFO: 1******** - - [11/Jun/2025 03:38:11] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:11,880 INFO: 1******** - - [11/Jun/2025 03:38:11] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:11,881 INFO: 1******** - - [11/Jun/2025 03:38:11] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:12,140 INFO: 1******** - - [11/Jun/2025 03:38:12] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:38:12,271 INFO: 1******** - - [11/Jun/2025 03:38:12] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:38:12,605 INFO: 1******** - - [11/Jun/2025 03:38:12] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:38:12,857 INFO: 1******** - - [11/Jun/2025 03:38:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:12,955 INFO: 1******** - - [11/Jun/2025 03:38:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:12,957 INFO: 1******** - - [11/Jun/2025 03:38:12] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:12,959 INFO: 1******** - - [11/Jun/2025 03:38:12] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:12,974 INFO: 1******** - - [11/Jun/2025 03:38:12] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:13,279 INFO: 1******** - - [11/Jun/2025 03:38:13] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:20,817 INFO: 1******** - - [11/Jun/2025 03:38:20] "GET /login HTTP/1.1" 200 -
2025-06-11 03:38:21,077 INFO: 1******** - - [11/Jun/2025 03:38:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:21,172 INFO: 1******** - - [11/Jun/2025 03:38:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:25,906 INFO: 1******** - - [11/Jun/2025 03:38:25] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 03:38:26,264 INFO: 1******** - - [11/Jun/2025 03:38:26] "GET /admin HTTP/1.1" 200 -
2025-06-11 03:38:26,480 INFO: 1******** - - [11/Jun/2025 03:38:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:26,640 INFO: 1******** - - [11/Jun/2025 03:38:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:27,714 INFO: 1******** - - [11/Jun/2025 03:38:27] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 03:38:28,073 INFO: 1******** - - [11/Jun/2025 03:38:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:28,073 INFO: 1******** - - [11/Jun/2025 03:38:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:31,589 INFO: 1******** - - [11/Jun/2025 03:38:31] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 03:38:31,954 INFO: 1******** - - [11/Jun/2025 03:38:31] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:38:31,956 INFO: 1******** - - [11/Jun/2025 03:38:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:38:31,956 INFO: 1******** - - [11/Jun/2025 03:38:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:38:31,957 INFO: 1******** - - [11/Jun/2025 03:38:31] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:38:32,201 INFO: 1******** - - [11/Jun/2025 03:38:32] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:38:33,561 INFO: 1******** - - [11/Jun/2025 03:38:33] "[35m[1mGET /admin/edit_app/11 HTTP/1.1[0m" 500 -
2025-06-11 03:38:33,911 INFO: 1******** - - [11/Jun/2025 03:38:33] "GET /admin/edit_app/11?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-11 03:38:33,912 INFO: 1******** - - [11/Jun/2025 03:38:33] "GET /admin/edit_app/11?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-11 03:38:34,173 INFO: 1******** - - [11/Jun/2025 03:38:34] "GET /admin/edit_app/11?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-11 03:38:34,305 INFO: 1******** - - [11/Jun/2025 03:38:34] "[36mGET /admin/edit_app/11?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 03:40:47,130 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:40:47,131 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:40:47,144 INFO:  * Restarting with stat
2025-06-11 03:40:47,973 WARNING:  * Debugger is active!
2025-06-11 03:40:47,996 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:40:48,235 INFO: 1******** - - [11/Jun/2025 03:40:48] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:40:48,417 INFO: 1******** - - [11/Jun/2025 03:40:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:40:48,591 INFO: 1******** - - [11/Jun/2025 03:40:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:40:48,606 INFO: 1******** - - [11/Jun/2025 03:40:48] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:40:48,607 INFO: 1******** - - [11/Jun/2025 03:40:48] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:40:48,917 INFO: 1******** - - [11/Jun/2025 03:40:48] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:40:48,918 INFO: 1******** - - [11/Jun/2025 03:40:48] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:40:49,245 INFO: 1******** - - [11/Jun/2025 03:40:49] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:40:49,632 INFO: 1******** - - [11/Jun/2025 03:40:49] "[32mGET /download/11 HTTP/1.1[0m" 302 -
2025-06-11 03:40:49,971 INFO: 1******** - - [11/Jun/2025 03:40:49] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:40:50,224 INFO: 1******** - - [11/Jun/2025 03:40:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:40:50,364 INFO: 1******** - - [11/Jun/2025 03:40:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:40:50,369 INFO: 1******** - - [11/Jun/2025 03:40:50] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:40:50,369 INFO: 1******** - - [11/Jun/2025 03:40:50] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:40:50,387 INFO: 1******** - - [11/Jun/2025 03:40:50] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:40:50,702 INFO: 1******** - - [11/Jun/2025 03:40:50] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:45:29,246 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 03:45:29,247 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:45:29,260 INFO:  * Restarting with stat
2025-06-11 03:45:30,136 WARNING:  * Debugger is active!
2025-06-11 03:45:30,159 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:45:30,391 INFO: 1******** - - [11/Jun/2025 03:45:30] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:45:30,620 INFO: 1******** - - [11/Jun/2025 03:45:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:45:30,750 INFO: 1******** - - [11/Jun/2025 03:45:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:45:30,753 INFO: 1******** - - [11/Jun/2025 03:45:30] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:45:30,755 INFO: 1******** - - [11/Jun/2025 03:45:30] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:45:31,071 INFO: 1******** - - [11/Jun/2025 03:45:31] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:45:31,072 INFO: 1******** - - [11/Jun/2025 03:45:31] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:45:31,333 INFO: 1******** - - [11/Jun/2025 03:45:31] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:45:31,523 INFO: 1******** - - [11/Jun/2025 03:45:31] "GET /download/11 HTTP/1.1" 200 -
2025-06-11 03:48:35,232 INFO: 1******** - - [11/Jun/2025 03:48:35] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:48:35,484 INFO: 1******** - - [11/Jun/2025 03:48:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:35,597 INFO: 1******** - - [11/Jun/2025 03:48:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:48:35,599 INFO: 1******** - - [11/Jun/2025 03:48:35] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:35,600 INFO: 1******** - - [11/Jun/2025 03:48:35] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:35,621 INFO: 1******** - - [11/Jun/2025 03:48:35] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:35,935 INFO: 1******** - - [11/Jun/2025 03:48:35] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:36,260 INFO: 1******** - - [11/Jun/2025 03:48:36] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:48:36,638 INFO: 1******** - - [11/Jun/2025 03:48:36] "GET /login HTTP/1.1" 200 -
2025-06-11 03:48:37,008 INFO: 1******** - - [11/Jun/2025 03:48:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:37,009 INFO: 1******** - - [11/Jun/2025 03:48:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:48:40,551 INFO: 1******** - - [11/Jun/2025 03:48:40] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 03:48:40,912 INFO: 1******** - - [11/Jun/2025 03:48:40] "GET /admin HTTP/1.1" 200 -
2025-06-11 03:48:41,131 INFO: 1******** - - [11/Jun/2025 03:48:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:41,271 INFO: 1******** - - [11/Jun/2025 03:48:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:48:44,377 INFO: 1******** - - [11/Jun/2025 03:48:44] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 03:48:44,732 INFO: 1******** - - [11/Jun/2025 03:48:44] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:48:44,734 INFO: 1******** - - [11/Jun/2025 03:48:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:48:44,734 INFO: 1******** - - [11/Jun/2025 03:48:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:44,736 INFO: 1******** - - [11/Jun/2025 03:48:44] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:44,994 INFO: 1******** - - [11/Jun/2025 03:48:44] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:48:45,857 INFO: 1******** - - [11/Jun/2025 03:48:45] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 03:48:46,213 INFO: 1******** - - [11/Jun/2025 03:48:46] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:46,214 INFO: 1******** - - [11/Jun/2025 03:48:46] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:46,214 INFO: 1******** - - [11/Jun/2025 03:48:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:46,215 INFO: 1******** - - [11/Jun/2025 03:48:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:48:46,468 INFO: 1******** - - [11/Jun/2025 03:48:46] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:46,560 INFO: 1******** - - [11/Jun/2025 03:48:46] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:48,443 INFO: 1******** - - [11/Jun/2025 03:48:48] "GET /admin/edit_app/11 HTTP/1.1" 200 -
2025-06-11 03:48:48,793 INFO: 1******** - - [11/Jun/2025 03:48:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:48:48,794 INFO: 1******** - - [11/Jun/2025 03:48:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:48,794 INFO: 1******** - - [11/Jun/2025 03:48:48] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:54,964 INFO: 1******** - - [11/Jun/2025 03:48:54] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 03:48:55,321 INFO: 1******** - - [11/Jun/2025 03:48:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:55,321 INFO: 1******** - - [11/Jun/2025 03:48:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:48:55,361 INFO: 1******** - - [11/Jun/2025 03:48:55] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:48:55,363 INFO: 1******** - - [11/Jun/2025 03:48:55] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:48:55,581 INFO: 1******** - - [11/Jun/2025 03:48:55] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:48:59,043 INFO: 1******** - - [11/Jun/2025 03:48:59] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 03:48:59,401 INFO: 1******** - - [11/Jun/2025 03:48:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:48:59,402 INFO: 1******** - - [11/Jun/2025 03:48:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:49:00,785 INFO: 1******** - - [11/Jun/2025 03:49:00] "[35m[1mGET /admin/edit_user/3 HTTP/1.1[0m" 500 -
2025-06-11 03:49:01,136 INFO: 1******** - - [11/Jun/2025 03:49:01] "GET /admin/edit_user/3?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-11 03:49:01,137 INFO: 1******** - - [11/Jun/2025 03:49:01] "GET /admin/edit_user/3?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-11 03:49:01,398 INFO: 1******** - - [11/Jun/2025 03:49:01] "GET /admin/edit_user/3?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-11 03:49:01,525 INFO: 1******** - - [11/Jun/2025 03:49:01] "[36mGET /admin/edit_user/3?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 03:49:43,448 INFO: 1******** - - [11/Jun/2025 03:49:43] "GET /admin/edit_user/3 HTTP/1.1" 200 -
2025-06-11 03:49:46,845 INFO: 1******** - - [11/Jun/2025 03:49:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:49:46,845 INFO: 1******** - - [11/Jun/2025 03:49:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:49:47,107 INFO: 1******** - - [11/Jun/2025 03:49:47] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:50:02,215 INFO: 1******** - - [11/Jun/2025 03:50:02] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 03:50:02,483 INFO: 1******** - - [11/Jun/2025 03:50:02] "GET /gate HTTP/1.1" 200 -
2025-06-11 03:50:02,525 INFO: 1******** - - [11/Jun/2025 03:50:02] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 03:50:02,825 INFO: 1******** - - [11/Jun/2025 03:50:02] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 03:50:02,826 INFO: 1******** - - [11/Jun/2025 03:50:02] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-11 03:50:03,627 INFO: 1******** - - [11/Jun/2025 03:50:03] "POST /registerf HTTP/1.1" 200 -
2025-06-11 03:50:03,999 INFO: 1******** - - [11/Jun/2025 03:50:03] "GET / HTTP/1.1" 200 -
2025-06-11 03:50:04,205 INFO: 1******** - - [11/Jun/2025 03:50:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:04,353 INFO: 1******** - - [11/Jun/2025 03:50:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:04,355 INFO: 1******** - - [11/Jun/2025 03:50:04] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:50:04,361 INFO: 1******** - - [11/Jun/2025 03:50:04] "GET /uploads/icons/20250611_033145_black.webp HTTP/1.1" 200 -
2025-06-11 03:50:04,368 INFO: 1******** - - [11/Jun/2025 03:50:04] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:50:04,843 INFO: 1******** - - [11/Jun/2025 03:50:04] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-11 03:50:05,107 INFO: 1******** - - [11/Jun/2025 03:50:05] "GET /login HTTP/1.1" 200 -
2025-06-11 03:50:05,250 INFO: 1******** - - [11/Jun/2025 03:50:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:05,437 INFO: 1******** - - [11/Jun/2025 03:50:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:05,508 INFO: 1******** - - [11/Jun/2025 03:50:05] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:50:15,694 INFO: 1******** - - [11/Jun/2025 03:50:15] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 03:50:15,804 INFO: 1******** - - [11/Jun/2025 03:50:15] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:50:16,009 INFO: 1******** - - [11/Jun/2025 03:50:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:16,141 INFO: 1******** - - [11/Jun/2025 03:50:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:16,230 INFO: 1******** - - [11/Jun/2025 03:50:16] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:50:18,433 INFO: 1******** - - [11/Jun/2025 03:50:18] "GET /publisher/edit_app/10 HTTP/1.1" 200 -
2025-06-11 03:50:18,671 INFO: 1******** - - [11/Jun/2025 03:50:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:18,782 INFO: 1******** - - [11/Jun/2025 03:50:18] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:50:18,784 INFO: 1******** - - [11/Jun/2025 03:50:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:18,871 INFO: 1******** - - [11/Jun/2025 03:50:18] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:50:22,310 INFO: 1******** - - [11/Jun/2025 03:50:22] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:50:22,552 INFO: 1******** - - [11/Jun/2025 03:50:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:22,666 INFO: 1******** - - [11/Jun/2025 03:50:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:22,735 INFO: 1******** - - [11/Jun/2025 03:50:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:50:31,010 INFO: 1******** - - [11/Jun/2025 03:50:31] "[32mPOST /admin/edit_user/3 HTTP/1.1[0m" 302 -
2025-06-11 03:50:31,350 INFO: 1******** - - [11/Jun/2025 03:50:31] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 03:50:31,599 INFO: 1******** - - [11/Jun/2025 03:50:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:31,720 INFO: 1******** - - [11/Jun/2025 03:50:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:31,773 INFO: 1******** - - [11/Jun/2025 03:50:31] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:50:32,034 INFO: 1******** - - [11/Jun/2025 03:50:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:32,115 INFO: 1******** - - [11/Jun/2025 03:50:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:32,191 INFO: 1******** - - [11/Jun/2025 03:50:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:50:33,494 INFO: 1******** - - [11/Jun/2025 03:50:33] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:50:33,754 INFO: 1******** - - [11/Jun/2025 03:50:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:50:33,829 INFO: 1******** - - [11/Jun/2025 03:50:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:50:33,900 INFO: 1******** - - [11/Jun/2025 03:50:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:53:01,512 INFO: 1******** - - [11/Jun/2025 03:53:01] "GET / HTTP/1.1" 200 -
2025-06-11 03:53:01,762 INFO: 1******** - - [11/Jun/2025 03:53:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:53:01,907 INFO: 1******** - - [11/Jun/2025 03:53:01] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 03:53:01,909 INFO: 1******** - - [11/Jun/2025 03:53:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:53:01,909 INFO: 1******** - - [11/Jun/2025 03:53:01] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 03:53:01,925 INFO: 1******** - - [11/Jun/2025 03:53:01] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:53:02,419 INFO: 1******** - - [11/Jun/2025 03:53:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:53:08,865 INFO: 1******** - - [11/Jun/2025 03:53:08] "GET /app/4 HTTP/1.1" 200 -
2025-06-11 03:53:09,230 INFO: 1******** - - [11/Jun/2025 03:53:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:53:09,230 INFO: 1******** - - [11/Jun/2025 03:53:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:53:09,494 INFO: 1******** - - [11/Jun/2025 03:53:09] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:53:21,860 INFO: 1******** - - [11/Jun/2025 03:53:21] "[32mPOST /app/4/rate HTTP/1.1[0m" 302 -
2025-06-11 03:53:22,200 INFO: 1******** - - [11/Jun/2025 03:53:22] "GET /app/4 HTTP/1.1" 200 -
2025-06-11 03:53:22,433 INFO: 1******** - - [11/Jun/2025 03:53:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:53:22,541 INFO: 1******** - - [11/Jun/2025 03:53:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:53:22,630 INFO: 1******** - - [11/Jun/2025 03:53:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:53:30,748 INFO: 1******** - - [11/Jun/2025 03:53:30] "[32mPOST /app/4/rate HTTP/1.1[0m" 302 -
2025-06-11 03:53:31,007 INFO: 1******** - - [11/Jun/2025 03:53:31] "GET /app/4 HTTP/1.1" 200 -
2025-06-11 03:53:31,056 INFO: 1******** - - [11/Jun/2025 03:53:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:53:31,352 INFO: 1******** - - [11/Jun/2025 03:53:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:53:31,438 INFO: 1******** - - [11/Jun/2025 03:53:31] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:53:41,291 INFO: 1******** - - [11/Jun/2025 03:53:41] "[32mPOST /app/4/rate HTTP/1.1[0m" 302 -
2025-06-11 03:53:41,539 INFO: 1******** - - [11/Jun/2025 03:53:41] "GET /app/4 HTTP/1.1" 200 -
2025-06-11 03:53:41,603 INFO: 1******** - - [11/Jun/2025 03:53:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:53:41,867 INFO: 1******** - - [11/Jun/2025 03:53:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:53:41,944 INFO: 1******** - - [11/Jun/2025 03:53:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:54:25,115 INFO: 1******** - - [11/Jun/2025 03:54:25] "GET /app/4 HTTP/1.1" 200 -
2025-06-11 03:54:25,368 INFO: 1******** - - [11/Jun/2025 03:54:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:54:25,455 INFO: 1******** - - [11/Jun/2025 03:54:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:54:25,545 INFO: 1******** - - [11/Jun/2025 03:54:25] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:54:27,869 INFO: 1******** - - [11/Jun/2025 03:54:27] "GET /app/4 HTTP/1.1" 200 -
2025-06-11 03:54:28,103 INFO: 1******** - - [11/Jun/2025 03:54:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:54:28,213 INFO: 1******** - - [11/Jun/2025 03:54:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:54:28,290 INFO: 1******** - - [11/Jun/2025 03:54:28] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:54:34,480 INFO: 1******** - - [11/Jun/2025 03:54:34] "GET /suggestions HTTP/1.1" 200 -
2025-06-11 03:54:34,741 INFO: 1******** - - [11/Jun/2025 03:54:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:54:34,839 INFO: 1******** - - [11/Jun/2025 03:54:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:54:34,926 INFO: 1******** - - [11/Jun/2025 03:54:34] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:54:38,062 INFO: 1******** - - [11/Jun/2025 03:54:38] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:55:46,108 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 03:55:46,219 INFO:  * Restarting with stat
2025-06-11 03:55:47,199 WARNING:  * Debugger is active!
2025-06-11 03:55:47,225 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:56:01,382 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 03:56:01,483 INFO:  * Restarting with stat
2025-06-11 03:56:02,711 WARNING:  * Debugger is active!
2025-06-11 03:56:02,735 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:56:14,348 INFO: 1******** - - [11/Jun/2025 03:56:14] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 03:56:14,723 INFO: 1******** - - [11/Jun/2025 03:56:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:14,826 INFO: 1******** - - [11/Jun/2025 03:56:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:14,860 INFO: 1******** - - [11/Jun/2025 03:56:14] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 03:56:17,302 INFO: 1******** - - [11/Jun/2025 03:56:17] "GET /admin HTTP/1.1" 200 -
2025-06-11 03:56:17,535 INFO: 1******** - - [11/Jun/2025 03:56:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:17,653 INFO: 1******** - - [11/Jun/2025 03:56:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:24,890 INFO: 1******** - - [11/Jun/2025 03:56:24] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 03:56:25,248 INFO: 1******** - - [11/Jun/2025 03:56:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:25,249 INFO: 1******** - - [11/Jun/2025 03:56:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:26,609 INFO: 1******** - - [11/Jun/2025 03:56:26] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 03:56:27,009 INFO: 1******** - - [11/Jun/2025 03:56:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:27,010 INFO: 1******** - - [11/Jun/2025 03:56:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:28,872 INFO: 1******** - - [11/Jun/2025 03:56:28] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 03:56:29,231 INFO: 1******** - - [11/Jun/2025 03:56:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:29,231 INFO: 1******** - - [11/Jun/2025 03:56:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:30,753 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 03:56:30,869 INFO:  * Restarting with stat
2025-06-11 03:56:31,975 WARNING:  * Debugger is active!
2025-06-11 03:56:31,998 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:56:34,148 INFO: 1******** - - [11/Jun/2025 03:56:34] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 03:56:34,435 INFO: 1******** - - [11/Jun/2025 03:56:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:34,504 INFO: 1******** - - [11/Jun/2025 03:56:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:43,342 INFO: 1******** - - [11/Jun/2025 03:56:43] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 03:56:44,025 INFO: 1******** - - [11/Jun/2025 03:56:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:44,060 INFO: 1******** - - [11/Jun/2025 03:56:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:46,975 INFO: 1******** - - [11/Jun/2025 03:56:46] "GET /admin HTTP/1.1" 200 -
2025-06-11 03:56:47,218 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:56:47,358 INFO:  * Restarting with stat
2025-06-11 03:56:48,521 WARNING:  * Debugger is active!
2025-06-11 03:56:48,582 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:56:48,870 INFO: 1******** - - [11/Jun/2025 03:56:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:48,880 INFO: 1******** - - [11/Jun/2025 03:56:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:56:51,911 INFO: 1******** - - [11/Jun/2025 03:56:51] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 03:56:52,840 INFO: 1******** - - [11/Jun/2025 03:56:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:56:52,840 INFO: 1******** - - [11/Jun/2025 03:56:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:57:07,997 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:57:08,102 INFO:  * Restarting with stat
2025-06-11 03:57:09,423 WARNING:  * Debugger is active!
2025-06-11 03:57:09,453 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:57:27,690 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:57:27,784 INFO:  * Restarting with stat
2025-06-11 03:57:28,873 WARNING:  * Debugger is active!
2025-06-11 03:57:28,895 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:57:55,643 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 03:57:55,797 INFO:  * Restarting with stat
2025-06-11 03:57:56,883 WARNING:  * Debugger is active!
2025-06-11 03:57:56,905 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:58:10,931 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 03:58:11,026 INFO:  * Restarting with stat
2025-06-11 03:58:12,231 WARNING:  * Debugger is active!
2025-06-11 03:58:12,252 INFO:  * Debugger PIN: 454-249-677
2025-06-11 03:58:22,999 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 03:58:23,091 INFO:  * Restarting with stat
2025-06-11 03:58:23,994 WARNING:  * Debugger is active!
2025-06-11 03:58:24,015 INFO:  * Debugger PIN: 454-249-677
2025-06-11 04:00:23,362 INFO: 1******** - - [11/Jun/2025 04:00:23] "GET /admin HTTP/1.1" 200 -
2025-06-11 04:00:23,724 INFO: 1******** - - [11/Jun/2025 04:00:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:00:23,853 INFO: 1******** - - [11/Jun/2025 04:00:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:00:23,923 INFO: 1******** - - [11/Jun/2025 04:00:23] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:03:17,366 INFO: 1******** - - [11/Jun/2025 04:03:17] "GET /admin HTTP/1.1" 200 -
2025-06-11 04:03:17,598 INFO: 1******** - - [11/Jun/2025 04:03:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:03:17,706 INFO: 1******** - - [11/Jun/2025 04:03:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:03:17,742 INFO: 1******** - - [11/Jun/2025 04:03:17] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:03:20,502 INFO: 1******** - - [11/Jun/2025 04:03:20] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 04:03:20,745 INFO: 1******** - - [11/Jun/2025 04:03:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:03:20,873 INFO: 1******** - - [11/Jun/2025 04:03:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:03:24,717 INFO: 1******** - - [11/Jun/2025 04:03:24] "GET /admin HTTP/1.1" 200 -
2025-06-11 04:03:25,066 INFO: 1******** - - [11/Jun/2025 04:03:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:03:25,067 INFO: 1******** - - [11/Jun/2025 04:03:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:03:28,635 INFO: 1******** - - [11/Jun/2025 04:03:28] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 04:03:28,815 INFO: 1******** - - [11/Jun/2025 04:03:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:03:28,993 INFO: 1******** - - [11/Jun/2025 04:03:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:03,389 INFO: 1******** - - [11/Jun/2025 04:04:03] "POST /admin/reports/update_status/1 HTTP/1.1" 200 -
2025-06-11 04:04:04,725 INFO: 1******** - - [11/Jun/2025 04:04:04] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 04:04:05,084 INFO: 1******** - - [11/Jun/2025 04:04:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:05,085 INFO: 1******** - - [11/Jun/2025 04:04:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:05,330 INFO: 1******** - - [11/Jun/2025 04:04:05] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:04:10,428 INFO: 1******** - - [11/Jun/2025 04:04:10] "POST /admin/reports/update_status/1 HTTP/1.1" 200 -
2025-06-11 04:04:11,315 INFO: 1******** - - [11/Jun/2025 04:04:11] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 04:04:11,667 INFO: 1******** - - [11/Jun/2025 04:04:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:11,668 INFO: 1******** - - [11/Jun/2025 04:04:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:11,912 INFO: 1******** - - [11/Jun/2025 04:04:11] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:04:14,815 INFO: 1******** - - [11/Jun/2025 04:04:14] "GET /admin/reports?type=abuse&status=resolved HTTP/1.1" 200 -
2025-06-11 04:04:15,159 INFO: 1******** - - [11/Jun/2025 04:04:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:15,160 INFO: 1******** - - [11/Jun/2025 04:04:15] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:16,155 INFO: 1******** - - [11/Jun/2025 04:04:16] "GET /admin/reports?type=abuse&status=dismissed HTTP/1.1" 200 -
2025-06-11 04:04:16,510 INFO: 1******** - - [11/Jun/2025 04:04:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:16,511 INFO: 1******** - - [11/Jun/2025 04:04:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:18,737 INFO: 1******** - - [11/Jun/2025 04:04:18] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 04:04:19,092 INFO: 1******** - - [11/Jun/2025 04:04:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:19,093 INFO: 1******** - - [11/Jun/2025 04:04:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:26,168 INFO: 1******** - - [11/Jun/2025 04:04:26] "GET / HTTP/1.1" 200 -
2025-06-11 04:04:26,630 INFO: 1******** - - [11/Jun/2025 04:04:26] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:04:26,632 INFO: 1******** - - [11/Jun/2025 04:04:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:26,634 INFO: 1******** - - [11/Jun/2025 04:04:26] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 04:04:26,634 INFO: 1******** - - [11/Jun/2025 04:04:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:26,872 INFO: 1******** - - [11/Jun/2025 04:04:26] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:04:30,561 INFO: 1******** - - [11/Jun/2025 04:04:30] "GET /app/11 HTTP/1.1" 200 -
2025-06-11 04:04:30,929 INFO: 1******** - - [11/Jun/2025 04:04:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:30,929 INFO: 1******** - - [11/Jun/2025 04:04:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:30,930 INFO: 1******** - - [11/Jun/2025 04:04:30] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 04:04:30,931 INFO: 1******** - - [11/Jun/2025 04:04:30] "[36mGET /uploads/screenshots/20250611_033146_fd38d66b-ebae-437a-983d-8845101cc6cd.webp HTTP/1.1[0m" 304 -
2025-06-11 04:04:31,172 INFO: 1******** - - [11/Jun/2025 04:04:31] "[36mGET /uploads/screenshots/20250611_033146_black.webp HTTP/1.1[0m" 304 -
2025-06-11 04:04:31,280 INFO: 1******** - - [11/Jun/2025 04:04:31] "[36mGET /uploads/screenshots/20250611_033146_image__1_.webp HTTP/1.1[0m" 304 -
2025-06-11 04:04:32,660 INFO: 1******** - - [11/Jun/2025 04:04:32] "GET /suggestions HTTP/1.1" 200 -
2025-06-11 04:04:33,012 INFO: 1******** - - [11/Jun/2025 04:04:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:33,013 INFO: 1******** - - [11/Jun/2025 04:04:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:39,808 INFO: 1******** - - [11/Jun/2025 04:04:39] "POST /suggestions HTTP/1.1" 200 -
2025-06-11 04:04:40,930 INFO: 1******** - - [11/Jun/2025 04:04:40] "GET / HTTP/1.1" 200 -
2025-06-11 04:04:41,279 INFO: 1******** - - [11/Jun/2025 04:04:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:41,292 INFO: 1******** - - [11/Jun/2025 04:04:41] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:04:41,294 INFO: 1******** - - [11/Jun/2025 04:04:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:41,294 INFO: 1******** - - [11/Jun/2025 04:04:41] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 04:04:41,546 INFO: 1******** - - [11/Jun/2025 04:04:41] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:04:43,861 INFO: 1******** - - [11/Jun/2025 04:04:43] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 04:04:44,214 INFO: 1******** - - [11/Jun/2025 04:04:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:04:44,214 INFO: 1******** - - [11/Jun/2025 04:04:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:04:44,460 INFO: 1******** - - [11/Jun/2025 04:04:44] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:10,702 INFO: 1******** - - [11/Jun/2025 04:05:10] "[32mGET /admin/ HTTP/1.1[0m" 302 -
2025-06-11 04:05:10,958 INFO: 1******** - - [11/Jun/2025 04:05:10] "GET /gate HTTP/1.1" 200 -
2025-06-11 04:05:11,016 INFO: 1******** - - [11/Jun/2025 04:05:11] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 04:05:11,302 INFO: 1******** - - [11/Jun/2025 04:05:11] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 04:05:11,302 INFO: 1******** - - [11/Jun/2025 04:05:11] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-11 04:05:11,930 INFO: 1******** - - [11/Jun/2025 04:05:11] "POST /registerf HTTP/1.1" 200 -
2025-06-11 04:05:12,256 INFO: 1******** - - [11/Jun/2025 04:05:12] "GET / HTTP/1.1" 200 -
2025-06-11 04:05:12,509 INFO: 1******** - - [11/Jun/2025 04:05:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:12,591 INFO: 1******** - - [11/Jun/2025 04:05:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:12,595 INFO: 1******** - - [11/Jun/2025 04:05:12] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:05:12,597 INFO: 1******** - - [11/Jun/2025 04:05:12] "GET /uploads/icons/20250611_033145_black.webp HTTP/1.1" 200 -
2025-06-11 04:05:12,609 INFO: 1******** - - [11/Jun/2025 04:05:12] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:05:13,080 INFO: 1******** - - [11/Jun/2025 04:05:13] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-11 04:05:13,610 INFO: 1******** - - [11/Jun/2025 04:05:13] "[33mGET /admin/ HTTP/1.1[0m" 404 -
2025-06-11 04:05:15,489 INFO: 1******** - - [11/Jun/2025 04:05:15] "[33mGET /admin/ HTTP/1.1[0m" 404 -
2025-06-11 04:05:17,260 INFO: 1******** - - [11/Jun/2025 04:05:17] "GET /login HTTP/1.1" 200 -
2025-06-11 04:05:17,502 INFO: 1******** - - [11/Jun/2025 04:05:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:17,609 INFO: 1******** - - [11/Jun/2025 04:05:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:17,679 INFO: 1******** - - [11/Jun/2025 04:05:17] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:21,848 INFO: 1******** - - [11/Jun/2025 04:05:21] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 04:05:21,951 INFO: 1******** - - [11/Jun/2025 04:05:21] "GET /admin HTTP/1.1" 200 -
2025-06-11 04:05:22,158 INFO: 1******** - - [11/Jun/2025 04:05:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:22,294 INFO: 1******** - - [11/Jun/2025 04:05:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:22,395 INFO: 1******** - - [11/Jun/2025 04:05:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:27,185 INFO: 1******** - - [11/Jun/2025 04:05:27] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-11 04:05:27,445 INFO: 1******** - - [11/Jun/2025 04:05:27] "GET / HTTP/1.1" 200 -
2025-06-11 04:05:27,495 INFO: 1******** - - [11/Jun/2025 04:05:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:27,795 INFO: 1******** - - [11/Jun/2025 04:05:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:27,798 INFO: 1******** - - [11/Jun/2025 04:05:27] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:05:27,800 INFO: 1******** - - [11/Jun/2025 04:05:27] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 04:05:27,813 INFO: 1******** - - [11/Jun/2025 04:05:27] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:05:28,211 INFO: 1******** - - [11/Jun/2025 04:05:28] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:29,025 INFO: 1******** - - [11/Jun/2025 04:05:29] "GET /login HTTP/1.1" 200 -
2025-06-11 04:05:29,363 INFO: 1******** - - [11/Jun/2025 04:05:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:29,371 INFO: 1******** - - [11/Jun/2025 04:05:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:29,629 INFO: 1******** - - [11/Jun/2025 04:05:29] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:34,687 INFO: 1******** - - [11/Jun/2025 04:05:34] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 04:05:35,036 INFO: 1******** - - [11/Jun/2025 04:05:35] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:05:35,264 INFO: 1******** - - [11/Jun/2025 04:05:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:35,386 INFO: 1******** - - [11/Jun/2025 04:05:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:35,467 INFO: 1******** - - [11/Jun/2025 04:05:35] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:39,082 INFO: 1******** - - [11/Jun/2025 04:05:39] "GET /publisher/posts HTTP/1.1" 200 -
2025-06-11 04:05:39,325 INFO: 1******** - - [11/Jun/2025 04:05:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:39,424 INFO: 1******** - - [11/Jun/2025 04:05:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:39,498 INFO: 1******** - - [11/Jun/2025 04:05:39] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:42,023 INFO: 1******** - - [11/Jun/2025 04:05:42] "GET /publisher/posts/add HTTP/1.1" 200 -
2025-06-11 04:05:42,251 INFO: 1******** - - [11/Jun/2025 04:05:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:42,402 INFO: 1******** - - [11/Jun/2025 04:05:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:42,496 INFO: 1******** - - [11/Jun/2025 04:05:42] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:47,602 INFO: 1******** - - [11/Jun/2025 04:05:47] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:05:47,864 INFO: 1******** - - [11/Jun/2025 04:05:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:47,947 INFO: 1******** - - [11/Jun/2025 04:05:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:48,024 INFO: 1******** - - [11/Jun/2025 04:05:48] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:05:56,453 INFO: 1******** - - [11/Jun/2025 04:05:56] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:05:56,691 INFO: 1******** - - [11/Jun/2025 04:05:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:05:56,801 INFO: 1******** - - [11/Jun/2025 04:05:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:05:56,876 INFO: 1******** - - [11/Jun/2025 04:05:56] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:08:03,150 INFO: 1******** - - [11/Jun/2025 04:08:03] "POST /publisher/shortlinks/create HTTP/1.1" 200 -
2025-06-11 04:08:04,931 INFO: 1******** - - [11/Jun/2025 04:08:04] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:08:05,273 INFO: 1******** - - [11/Jun/2025 04:08:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:08:05,274 INFO: 1******** - - [11/Jun/2025 04:08:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:08:05,536 INFO: 1******** - - [11/Jun/2025 04:08:05] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:08:09,938 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 04:08:10,053 INFO:  * Restarting with stat
2025-06-11 04:08:11,159 WARNING:  * Debugger is active!
2025-06-11 04:08:11,189 INFO:  * Debugger PIN: 454-249-677
2025-06-11 04:08:12,657 INFO: 1******** - - [11/Jun/2025 04:08:12] "[32mGET /s/HJV1JG HTTP/1.1[0m" 302 -
2025-06-11 04:08:14,956 INFO: 1******** - - [11/Jun/2025 04:08:14] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:08:15,505 INFO: 1******** - - [11/Jun/2025 04:08:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:08:15,505 INFO: 1******** - - [11/Jun/2025 04:08:15] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:08:15,583 INFO: 1******** - - [11/Jun/2025 04:08:15] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:08:22,025 INFO: 1******** - - [11/Jun/2025 04:08:22] "POST /publisher/shortlinks/delete/1 HTTP/1.1" 200 -
2025-06-11 04:08:23,274 INFO: 1******** - - [11/Jun/2025 04:08:23] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:08:23,623 INFO: 1******** - - [11/Jun/2025 04:08:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:08:23,624 INFO: 1******** - - [11/Jun/2025 04:08:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:08:23,882 INFO: 1******** - - [11/Jun/2025 04:08:23] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:08:41,557 INFO: 1******** - - [11/Jun/2025 04:08:41] "POST /publisher/shortlinks/create HTTP/1.1" 200 -
2025-06-11 04:08:43,333 INFO: 1******** - - [11/Jun/2025 04:08:43] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:08:43,687 INFO: 1******** - - [11/Jun/2025 04:08:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:08:43,688 INFO: 1******** - - [11/Jun/2025 04:08:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:08:43,950 INFO: 1******** - - [11/Jun/2025 04:08:43] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:08:52,084 INFO: 1******** - - [11/Jun/2025 04:08:52] "[31m[1mPOST /publisher/shortlinks/create HTTP/1.1[0m" 400 -
2025-06-11 04:08:55,443 INFO: 1******** - - [11/Jun/2025 04:08:55] "POST /publisher/shortlinks/create HTTP/1.1" 200 -
2025-06-11 04:08:56,677 INFO: 1******** - - [11/Jun/2025 04:08:56] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:08:56,935 INFO: 1******** - - [11/Jun/2025 04:08:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:08:57,025 INFO: 1******** - - [11/Jun/2025 04:08:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:08:57,115 INFO: 1******** - - [11/Jun/2025 04:08:57] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:01,443 INFO: 1******** - - [11/Jun/2025 04:09:01] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 04:09:01,578 INFO: 1******** - - [11/Jun/2025 04:09:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:01,786 INFO: 1******** - - [11/Jun/2025 04:09:01] "[33mGET /uploads/screenshots\\20250611_030305_01e2d015-e7ab-4fae-b174-513679ebe221.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:01,786 INFO: 1******** - - [11/Jun/2025 04:09:01] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:01,788 INFO: 1******** - - [11/Jun/2025 04:09:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:01,810 INFO: 1******** - - [11/Jun/2025 04:09:01] "[33mGET /uploads/screenshots\\20250611_030305_01e2d015-e7ab-4fae-b174-513679ebe221.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:02,444 INFO: 1******** - - [11/Jun/2025 04:09:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:02,893 INFO: 1******** - - [11/Jun/2025 04:09:02] "[32mGET /download/9 HTTP/1.1[0m" 302 -
2025-06-11 04:09:03,220 INFO: 1******** - - [11/Jun/2025 04:09:03] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 04:09:03,483 INFO: 1******** - - [11/Jun/2025 04:09:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:03,637 INFO: 1******** - - [11/Jun/2025 04:09:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:03,660 INFO: 1******** - - [11/Jun/2025 04:09:03] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:03,677 INFO: 1******** - - [11/Jun/2025 04:09:03] "[33mGET /uploads/screenshots\\20250611_030305_01e2d015-e7ab-4fae-b174-513679ebe221.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:03,794 INFO: 1******** - - [11/Jun/2025 04:09:03] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:05,988 INFO: 1******** - - [11/Jun/2025 04:09:05] "[32mGET /download/9 HTTP/1.1[0m" 302 -
2025-06-11 04:09:06,252 INFO: 1******** - - [11/Jun/2025 04:09:06] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 04:09:06,298 INFO: 1******** - - [11/Jun/2025 04:09:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:06,606 INFO: 1******** - - [11/Jun/2025 04:09:06] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:06,607 INFO: 1******** - - [11/Jun/2025 04:09:06] "[33mGET /uploads/screenshots\\20250611_030305_01e2d015-e7ab-4fae-b174-513679ebe221.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:06,609 INFO: 1******** - - [11/Jun/2025 04:09:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:06,631 INFO: 1******** - - [11/Jun/2025 04:09:06] "[33mGET /uploads/screenshots\\20250611_030305_01e2d015-e7ab-4fae-b174-513679ebe221.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:09:07,035 INFO: 1******** - - [11/Jun/2025 04:09:07] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:15,406 INFO: 1******** - - [11/Jun/2025 04:09:15] "[32mGET /s/dfgdgdd HTTP/1.1[0m" 302 -
2025-06-11 04:09:19,657 INFO: 1******** - - [11/Jun/2025 04:09:19] "POST /publisher/shortlinks/delete/3 HTTP/1.1" 200 -
2025-06-11 04:09:20,632 INFO: 1******** - - [11/Jun/2025 04:09:20] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:09:20,992 INFO: 1******** - - [11/Jun/2025 04:09:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:20,993 INFO: 1******** - - [11/Jun/2025 04:09:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:21,254 INFO: 1******** - - [11/Jun/2025 04:09:21] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:22,985 INFO: 1******** - - [11/Jun/2025 04:09:22] "POST /publisher/shortlinks/delete/2 HTTP/1.1" 200 -
2025-06-11 04:09:23,929 INFO: 1******** - - [11/Jun/2025 04:09:23] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:09:24,190 INFO: 1******** - - [11/Jun/2025 04:09:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:24,284 INFO: 1******** - - [11/Jun/2025 04:09:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:24,351 INFO: 1******** - - [11/Jun/2025 04:09:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:24,985 INFO: 1******** - - [11/Jun/2025 04:09:24] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:09:25,226 INFO: 1******** - - [11/Jun/2025 04:09:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:25,319 INFO: 1******** - - [11/Jun/2025 04:09:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:25,399 INFO: 1******** - - [11/Jun/2025 04:09:25] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:41,730 INFO: 1******** - - [11/Jun/2025 04:09:41] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:09:41,971 INFO: 1******** - - [11/Jun/2025 04:09:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:42,083 INFO: 1******** - - [11/Jun/2025 04:09:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:42,164 INFO: 1******** - - [11/Jun/2025 04:09:42] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:52,146 INFO: 1******** - - [11/Jun/2025 04:09:52] "GET /publisher/edit_app/10 HTTP/1.1" 200 -
2025-06-11 04:09:52,383 INFO: 1******** - - [11/Jun/2025 04:09:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:52,538 INFO: 1******** - - [11/Jun/2025 04:09:52] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:52,539 INFO: 1******** - - [11/Jun/2025 04:09:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:52,651 INFO: 1******** - - [11/Jun/2025 04:09:52] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:09:56,401 INFO: 1******** - - [11/Jun/2025 04:09:56] "[32mGET /download/10 HTTP/1.1[0m" 302 -
2025-06-11 04:09:56,666 INFO: 1******** - - [11/Jun/2025 04:09:56] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 04:09:56,711 INFO: 1******** - - [11/Jun/2025 04:09:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:09:57,017 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,018 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,019 INFO: 1******** - - [11/Jun/2025 04:09:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:09:57,031 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,342 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,345 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,348 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,349 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,350 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,414 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,661 INFO: 1******** - - [11/Jun/2025 04:09:57] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 404 -
2025-06-11 04:09:57,985 INFO: 1******** - - [11/Jun/2025 04:09:57] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:10:02,645 INFO: 1******** - - [11/Jun/2025 04:10:02] "[32mPOST /app/10/rate HTTP/1.1[0m" 302 -
2025-06-11 04:10:02,981 INFO: 1******** - - [11/Jun/2025 04:10:02] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 04:10:03,225 INFO: 1******** - - [11/Jun/2025 04:10:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:10:03,323 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,323 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,325 INFO: 1******** - - [11/Jun/2025 04:10:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:10:03,333 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,653 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,654 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,654 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,655 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,655 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:03,657 INFO: 1******** - - [11/Jun/2025 04:10:03] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:04,007 INFO: 1******** - - [11/Jun/2025 04:10:04] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:10:10,151 INFO: 1******** - - [11/Jun/2025 04:10:10] "[32mPOST /app/10/rate HTTP/1.1[0m" 302 -
2025-06-11 04:10:10,415 INFO: 1******** - - [11/Jun/2025 04:10:10] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 04:10:10,463 INFO: 1******** - - [11/Jun/2025 04:10:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:10:10,748 INFO: 1******** - - [11/Jun/2025 04:10:10] "[33mGET /uploads/icons/20250611_031819_d73ff7ad-c496-4746-913d-cd1168f5276b.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:10,748 INFO: 1******** - - [11/Jun/2025 04:10:10] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:10,750 INFO: 1******** - - [11/Jun/2025 04:10:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:10:10,762 INFO: 1******** - - [11/Jun/2025 04:10:10] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365439.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:11,082 INFO: 1******** - - [11/Jun/2025 04:10:11] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365440.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:11,084 INFO: 1******** - - [11/Jun/2025 04:10:11] "[33mGET /uploads/screenshots/20250611_031820_5470084071062365445.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:11,084 INFO: 1******** - - [11/Jun/2025 04:10:11] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350697.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:11,085 INFO: 1******** - - [11/Jun/2025 04:10:11] "[33mGET /uploads/screenshots/20250611_031820_5197616963621350698.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:11,085 INFO: 1******** - - [11/Jun/2025 04:10:11] "[33mGET /uploads/screenshots/20250611_031820_5197216754273740709.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:11,086 INFO: 1******** - - [11/Jun/2025 04:10:11] "[33mGET /uploads/screenshots/20250611_031819_5470084071062365438.webp HTTP/1.1[0m" 404 -
2025-06-11 04:10:11,423 INFO: 1******** - - [11/Jun/2025 04:10:11] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:10:56,179 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 04:10:56,302 INFO:  * Restarting with stat
2025-06-11 04:10:57,685 WARNING:  * Debugger is active!
2025-06-11 04:10:57,716 INFO:  * Debugger PIN: 454-249-677
2025-06-11 04:11:21,353 INFO: 1******** - - [11/Jun/2025 04:11:21] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 04:11:21,682 INFO: 1******** - - [11/Jun/2025 04:11:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:11:21,723 INFO: 1******** - - [11/Jun/2025 04:11:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:11:39,951 INFO: 1******** - - [11/Jun/2025 04:11:39] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:11:40,178 INFO: 1******** - - [11/Jun/2025 04:11:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:11:40,304 INFO: 1******** - - [11/Jun/2025 04:11:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:11:40,411 INFO: 1******** - - [11/Jun/2025 04:11:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:11:42,233 INFO: 1******** - - [11/Jun/2025 04:11:42] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:11:42,467 INFO: 1******** - - [11/Jun/2025 04:11:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:11:42,589 INFO: 1******** - - [11/Jun/2025 04:11:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:11:42,686 INFO: 1******** - - [11/Jun/2025 04:11:42] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:11:52,417 INFO: 1******** - - [11/Jun/2025 04:11:52] "POST /publisher/shortlinks/create HTTP/1.1" 200 -
2025-06-11 04:11:53,329 INFO: 1******** - - [11/Jun/2025 04:11:53] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:11:53,688 INFO: 1******** - - [11/Jun/2025 04:11:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:11:53,689 INFO: 1******** - - [11/Jun/2025 04:11:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:11:53,930 INFO: 1******** - - [11/Jun/2025 04:11:53] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:12:07,043 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 04:12:07,181 INFO:  * Restarting with stat
2025-06-11 04:12:08,550 WARNING:  * Debugger is active!
2025-06-11 04:12:08,575 INFO:  * Debugger PIN: 454-249-677
2025-06-11 04:12:18,321 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 04:12:18,411 INFO:  * Restarting with stat
2025-06-11 04:12:19,461 WARNING:  * Debugger is active!
2025-06-11 04:12:19,500 INFO:  * Debugger PIN: 454-249-677
2025-06-11 04:13:35,062 INFO: 1******** - - [11/Jun/2025 04:13:35] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 04:13:35,407 INFO: 1******** - - [11/Jun/2025 04:13:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:13:35,417 INFO: 1******** - - [11/Jun/2025 04:13:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:13:35,501 INFO: 1******** - - [11/Jun/2025 04:13:35] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:13:37,599 INFO: 1******** - - [11/Jun/2025 04:13:37] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:13:37,836 INFO: 1******** - - [11/Jun/2025 04:13:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:13:37,947 INFO: 1******** - - [11/Jun/2025 04:13:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:13:38,040 INFO: 1******** - - [11/Jun/2025 04:13:38] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:13:41,648 INFO: 1******** - - [11/Jun/2025 04:13:41] "POST /publisher/apps/delete/10 HTTP/1.1" 200 -
2025-06-11 04:13:42,745 INFO: 1******** - - [11/Jun/2025 04:13:42] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:13:43,107 INFO: 1******** - - [11/Jun/2025 04:13:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:13:43,108 INFO: 1******** - - [11/Jun/2025 04:13:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:13:43,370 INFO: 1******** - - [11/Jun/2025 04:13:43] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:13:44,368 INFO: 1******** - - [11/Jun/2025 04:13:44] "GET /publisher/edit_app/9 HTTP/1.1" 200 -
2025-06-11 04:13:44,722 INFO: 1******** - - [11/Jun/2025 04:13:44] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:13:44,725 INFO: 1******** - - [11/Jun/2025 04:13:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:13:44,725 INFO: 1******** - - [11/Jun/2025 04:13:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:13:44,971 INFO: 1******** - - [11/Jun/2025 04:13:44] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:13:48,012 INFO: 1******** - - [11/Jun/2025 04:13:48] "GET /publisher HTTP/1.1" 200 -
2025-06-11 04:13:48,274 INFO: 1******** - - [11/Jun/2025 04:13:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:13:48,367 INFO: 1******** - - [11/Jun/2025 04:13:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:13:48,443 INFO: 1******** - - [11/Jun/2025 04:13:48] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:13:54,551 INFO: 1******** - - [11/Jun/2025 04:13:54] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 04:13:54,763 INFO: 1******** - - [11/Jun/2025 04:13:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:13:54,905 INFO: 1******** - - [11/Jun/2025 04:13:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:13:54,937 INFO: 1******** - - [11/Jun/2025 04:13:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:14:01,865 INFO: 1******** - - [11/Jun/2025 04:14:01] "GET / HTTP/1.1" 200 -
2025-06-11 04:14:02,073 INFO: 1******** - - [11/Jun/2025 04:14:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:14:02,236 INFO: 1******** - - [11/Jun/2025 04:14:02] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 04:14:02,238 INFO: 1******** - - [11/Jun/2025 04:14:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:14:02,239 INFO: 1******** - - [11/Jun/2025 04:14:02] "[36mGET /uploads/icons/20250611_033145_black.webp HTTP/1.1[0m" 304 -
2025-06-11 04:15:34,994 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***************:5000
2025-06-11 04:15:34,995 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 04:15:35,008 INFO:  * Restarting with stat
2025-06-11 04:15:35,883 WARNING:  * Debugger is active!
2025-06-11 04:15:35,908 INFO:  * Debugger PIN: 454-249-677
2025-06-11 04:15:36,037 INFO: 1******** - - [11/Jun/2025 04:15:36] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 04:15:36,085 INFO: 1******** - - [11/Jun/2025 04:15:36] "GET /gate HTTP/1.1" 200 -
2025-06-11 04:15:36,796 INFO: 1******** - - [11/Jun/2025 04:15:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:15:36,819 INFO: 1******** - - [11/Jun/2025 04:15:36] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 04:15:36,822 INFO: 1******** - - [11/Jun/2025 04:15:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:15:36,851 INFO: 1******** - - [11/Jun/2025 04:15:36] "POST /registerf HTTP/1.1" 200 -
2025-06-11 04:15:37,306 INFO: 1******** - - [11/Jun/2025 04:15:37] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 04:15:37,348 INFO: 1******** - - [11/Jun/2025 04:15:37] "GET / HTTP/1.1" 200 -
2025-06-11 04:15:37,566 INFO: 1******** - - [11/Jun/2025 04:15:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:15:37,705 INFO: 1******** - - [11/Jun/2025 04:15:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:15:38,646 INFO: 1******** - - [11/Jun/2025 04:15:38] "GET /login HTTP/1.1" 200 -
2025-06-11 04:15:39,002 INFO: 1******** - - [11/Jun/2025 04:15:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:15:39,003 INFO: 1******** - - [11/Jun/2025 04:15:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:15:44,155 INFO: 1******** - - [11/Jun/2025 04:15:44] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 04:15:44,502 INFO: 1******** - - [11/Jun/2025 04:15:44] "GET /admin HTTP/1.1" 200 -
2025-06-11 04:15:44,735 INFO: 1******** - - [11/Jun/2025 04:15:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:15:44,875 INFO: 1******** - - [11/Jun/2025 04:15:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:15:48,198 INFO: 1******** - - [11/Jun/2025 04:15:48] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 04:15:48,434 INFO: 1******** - - [11/Jun/2025 04:15:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:15:48,562 INFO: 1******** - - [11/Jun/2025 04:15:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:15:49,809 INFO: 1******** - - [11/Jun/2025 04:15:49] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-11 04:15:50,180 INFO: 1******** - - [11/Jun/2025 04:15:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:15:50,181 INFO: 1******** - - [11/Jun/2025 04:15:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:16:10,030 INFO: 1******** - - [11/Jun/2025 04:16:10] "[32mPOST /admin/add_user HTTP/1.1[0m" 302 -
2025-06-11 04:16:10,045 INFO: 1******** - - [11/Jun/2025 04:16:10] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 04:16:10,402 INFO: 1******** - - [11/Jun/2025 04:16:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:16:10,412 INFO: 1******** - - [11/Jun/2025 04:16:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:16:14,107 INFO: 1******** - - [11/Jun/2025 04:16:14] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-11 04:16:14,474 INFO: 1******** - - [11/Jun/2025 04:16:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:16:14,475 INFO: 1******** - - [11/Jun/2025 04:16:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 04:16:25,090 INFO: 1******** - - [11/Jun/2025 04:16:25] "[32mPOST /admin/add_user HTTP/1.1[0m" 302 -
2025-06-11 04:16:25,109 INFO: 1******** - - [11/Jun/2025 04:16:25] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 04:16:25,477 INFO: 1******** - - [11/Jun/2025 04:16:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 04:16:25,478 INFO: 1******** - - [11/Jun/2025 04:16:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:24:27,372 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 12:24:27,374 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 12:24:28,884 INFO: 1******** - - [11/Jun/2025 12:24:28] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 12:24:28,921 INFO: 1******** - - [11/Jun/2025 12:24:28] "GET /gate HTTP/1.1" 200 -
2025-06-11 12:24:29,094 INFO: 1******** - - [11/Jun/2025 12:24:29] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 12:24:29,100 INFO: 1******** - - [11/Jun/2025 12:24:29] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 12:24:29,107 INFO: 1******** - - [11/Jun/2025 12:24:29] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-11 12:24:29,347 INFO: 1******** - - [11/Jun/2025 12:24:29] "POST /registerf HTTP/1.1" 200 -
2025-06-11 12:24:29,372 INFO: 1******** - - [11/Jun/2025 12:24:29] "GET / HTTP/1.1" 200 -
2025-06-11 12:24:29,390 INFO: 1******** - - [11/Jun/2025 12:24:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:24:29,403 INFO: 1******** - - [11/Jun/2025 12:24:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:24:34,241 INFO: 1******** - - [11/Jun/2025 12:24:34] "GET /login HTTP/1.1" 200 -
2025-06-11 12:24:34,259 INFO: 1******** - - [11/Jun/2025 12:24:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:24:34,261 INFO: 1******** - - [11/Jun/2025 12:24:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:24:38,825 INFO: 1******** - - [11/Jun/2025 12:24:38] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 12:24:38,858 INFO: 1******** - - [11/Jun/2025 12:24:38] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:24:38,875 INFO: 1******** - - [11/Jun/2025 12:24:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:24:38,876 INFO: 1******** - - [11/Jun/2025 12:24:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:24:41,274 INFO: 1******** - - [11/Jun/2025 12:24:41] "GET / HTTP/1.1" 200 -
2025-06-11 12:24:41,302 INFO: 1******** - - [11/Jun/2025 12:24:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:24:41,306 INFO: 1******** - - [11/Jun/2025 12:24:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:25:01,111 INFO: 1******** - - [11/Jun/2025 12:25:01] "GET / HTTP/1.1" 200 -
2025-06-11 12:25:01,130 INFO: 1******** - - [11/Jun/2025 12:25:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:25:01,131 INFO: 1******** - - [11/Jun/2025 12:25:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:25:01,208 INFO: 1******** - - [11/Jun/2025 12:25:01] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-11 12:25:25,563 INFO: 1******** - - [11/Jun/2025 12:25:25] "GET / HTTP/1.1" 200 -
2025-06-11 12:25:25,576 INFO: 1******** - - [11/Jun/2025 12:25:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:25:25,578 INFO: 1******** - - [11/Jun/2025 12:25:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:25:25,666 INFO: 1******** - - [11/Jun/2025 12:25:25] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:25:27,542 INFO: 1******** - - [11/Jun/2025 12:25:27] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 12:25:27,567 INFO: 1******** - - [11/Jun/2025 12:25:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:25:27,574 INFO: 1******** - - [11/Jun/2025 12:25:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:25:29,374 INFO: 1******** - - [11/Jun/2025 12:25:29] "GET /suggestions HTTP/1.1" 200 -
2025-06-11 12:25:29,395 INFO: 1******** - - [11/Jun/2025 12:25:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:25:29,398 INFO: 1******** - - [11/Jun/2025 12:25:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:25:46,739 INFO: 1******** - - [11/Jun/2025 12:25:46] "POST /suggestions HTTP/1.1" 200 -
2025-06-11 12:25:47,566 INFO: 1******** - - [11/Jun/2025 12:25:47] "GET / HTTP/1.1" 200 -
2025-06-11 12:25:47,588 INFO: 1******** - - [11/Jun/2025 12:25:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:25:47,590 INFO: 1******** - - [11/Jun/2025 12:25:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:26:05,998 INFO: 1******** - - [11/Jun/2025 12:26:05] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 12:26:06,016 INFO: 1******** - - [11/Jun/2025 12:26:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:26:06,016 INFO: 1******** - - [11/Jun/2025 12:26:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:26:08,461 INFO: 1******** - - [11/Jun/2025 12:26:08] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:26:08,487 INFO: 1******** - - [11/Jun/2025 12:26:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:26:08,500 INFO: 1******** - - [11/Jun/2025 12:26:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:26:14,317 INFO: 1******** - - [11/Jun/2025 12:26:14] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 12:26:14,340 INFO: 1******** - - [11/Jun/2025 12:26:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:26:14,347 INFO: 1******** - - [11/Jun/2025 12:26:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:26:15,020 INFO: 1******** - - [11/Jun/2025 12:26:15] "[32mGET /admin/reports?type=suggestions HTTP/1.1[0m" 302 -
2025-06-11 12:26:15,031 INFO: 1******** - - [11/Jun/2025 12:26:15] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:26:15,048 INFO: 1******** - - [11/Jun/2025 12:26:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:26:15,049 INFO: 1******** - - [11/Jun/2025 12:26:15] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:31:47,722 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 12:31:47,725 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 12:31:54,315 INFO: 1******** - - [11/Jun/2025 12:31:54] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 12:31:54,347 INFO: 1******** - - [11/Jun/2025 12:31:54] "GET /gate HTTP/1.1" 200 -
2025-06-11 12:31:54,551 INFO: 1******** - - [11/Jun/2025 12:31:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:31:54,560 INFO: 1******** - - [11/Jun/2025 12:31:54] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 12:31:54,560 INFO: 1******** - - [11/Jun/2025 12:31:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:31:54,826 INFO: 1******** - - [11/Jun/2025 12:31:54] "POST /registerf HTTP/1.1" 200 -
2025-06-11 12:31:54,879 INFO: 1******** - - [11/Jun/2025 12:31:54] "GET / HTTP/1.1" 200 -
2025-06-11 12:31:54,915 INFO: 1******** - - [11/Jun/2025 12:31:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:31:54,916 INFO: 1******** - - [11/Jun/2025 12:31:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:31:56,996 INFO: 1******** - - [11/Jun/2025 12:31:56] "GET /login HTTP/1.1" 200 -
2025-06-11 12:31:57,039 INFO: 1******** - - [11/Jun/2025 12:31:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:31:57,041 INFO: 1******** - - [11/Jun/2025 12:31:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:00,816 INFO: 1******** - - [11/Jun/2025 12:32:00] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 12:32:00,868 INFO: 1******** - - [11/Jun/2025 12:32:00] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:32:00,978 INFO: 1******** - - [11/Jun/2025 12:32:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:00,979 INFO: 1******** - - [11/Jun/2025 12:32:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:02,671 INFO: 1******** - - [11/Jun/2025 12:32:02] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 12:32:02,707 INFO: 1******** - - [11/Jun/2025 12:32:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:02,708 INFO: 1******** - - [11/Jun/2025 12:32:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:03,361 INFO: 1******** - - [11/Jun/2025 12:32:03] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 12:32:03,386 INFO: 1******** - - [11/Jun/2025 12:32:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:03,388 INFO: 1******** - - [11/Jun/2025 12:32:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:06,058 INFO: 1******** - - [11/Jun/2025 12:32:06] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 12:32:06,100 INFO: 1******** - - [11/Jun/2025 12:32:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:06,100 INFO: 1******** - - [11/Jun/2025 12:32:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:07,125 INFO: 1******** - - [11/Jun/2025 12:32:07] "GET / HTTP/1.1" 200 -
2025-06-11 12:32:07,166 INFO: 1******** - - [11/Jun/2025 12:32:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:07,167 INFO: 1******** - - [11/Jun/2025 12:32:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:08,872 INFO: 1******** - - [11/Jun/2025 12:32:08] "GET /app/2 HTTP/1.1" 200 -
2025-06-11 12:32:08,921 INFO: 1******** - - [11/Jun/2025 12:32:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:08,922 INFO: 1******** - - [11/Jun/2025 12:32:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:10,900 INFO: 1******** - - [11/Jun/2025 12:32:10] "GET /suggestions HTTP/1.1" 200 -
2025-06-11 12:32:10,940 INFO: 1******** - - [11/Jun/2025 12:32:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:10,943 INFO: 1******** - - [11/Jun/2025 12:32:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:15,786 INFO: 1******** - - [11/Jun/2025 12:32:15] "POST /suggestions HTTP/1.1" 200 -
2025-06-11 12:32:16,726 INFO: 1******** - - [11/Jun/2025 12:32:16] "GET / HTTP/1.1" 200 -
2025-06-11 12:32:16,779 INFO: 1******** - - [11/Jun/2025 12:32:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:16,779 INFO: 1******** - - [11/Jun/2025 12:32:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:23,145 INFO: 1******** - - [11/Jun/2025 12:32:23] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:32:23,194 INFO: 1******** - - [11/Jun/2025 12:32:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:23,196 INFO: 1******** - - [11/Jun/2025 12:32:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:24,815 INFO: 1******** - - [11/Jun/2025 12:32:24] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 12:32:24,863 INFO: 1******** - - [11/Jun/2025 12:32:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:24,863 INFO: 1******** - - [11/Jun/2025 12:32:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:26,153 INFO: 1******** - - [11/Jun/2025 12:32:26] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 12:32:26,191 INFO: 1******** - - [11/Jun/2025 12:32:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:26,210 INFO: 1******** - - [11/Jun/2025 12:32:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:26,442 INFO: 1******** - - [11/Jun/2025 12:32:26] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 12:32:26,481 INFO: 1******** - - [11/Jun/2025 12:32:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:26,481 INFO: 1******** - - [11/Jun/2025 12:32:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:27,256 INFO: 1******** - - [11/Jun/2025 12:32:27] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 12:32:27,287 INFO: 1******** - - [11/Jun/2025 12:32:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:27,288 INFO: 1******** - - [11/Jun/2025 12:32:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:28,348 INFO: 1******** - - [11/Jun/2025 12:32:28] "GET / HTTP/1.1" 200 -
2025-06-11 12:32:28,398 INFO: 1******** - - [11/Jun/2025 12:32:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:28,399 INFO: 1******** - - [11/Jun/2025 12:32:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:30,321 INFO: 1******** - - [11/Jun/2025 12:32:30] "GET /app/3 HTTP/1.1" 200 -
2025-06-11 12:32:30,365 INFO: 1******** - - [11/Jun/2025 12:32:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:32:30,366 INFO: 1******** - - [11/Jun/2025 12:32:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:32:38,464 INFO: 1******** - - [11/Jun/2025 12:32:38] "POST /app/3/report HTTP/1.1" 200 -
2025-06-11 12:33:33,962 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 12:33:33,962 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 12:33:33,966 INFO:  * Restarting with stat
2025-06-11 12:33:34,401 WARNING:  * Debugger is active!
2025-06-11 12:33:34,411 INFO:  * Debugger PIN: 122-053-548
2025-06-11 12:33:34,593 INFO: 1******** - - [11/Jun/2025 12:33:34] "GET /app/3 HTTP/1.1" 200 -
2025-06-11 12:33:34,742 INFO: 1******** - - [11/Jun/2025 12:33:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:33:34,743 INFO: 1******** - - [11/Jun/2025 12:33:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:33:34,808 INFO: 1******** - - [11/Jun/2025 12:33:34] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:34:33,782 INFO: 1******** - - [11/Jun/2025 12:34:33] "GET /app/3 HTTP/1.1" 200 -
2025-06-11 12:34:33,820 INFO: 1******** - - [11/Jun/2025 12:34:33] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 12:34:33,820 INFO: 1******** - - [11/Jun/2025 12:34:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:34:33,884 INFO: 1******** - - [11/Jun/2025 12:34:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:34:50,108 INFO: 1******** - - [11/Jun/2025 12:34:50] "GET /app/3 HTTP/1.1" 200 -
2025-06-11 12:34:50,151 INFO: 1******** - - [11/Jun/2025 12:34:50] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 12:34:50,151 INFO: 1******** - - [11/Jun/2025 12:34:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:34:50,213 INFO: 1******** - - [11/Jun/2025 12:34:50] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:35:10,533 INFO: 1******** - - [11/Jun/2025 12:35:10] "GET /app/3 HTTP/1.1" 200 -
2025-06-11 12:35:10,570 INFO: 1******** - - [11/Jun/2025 12:35:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:35:10,571 INFO: 1******** - - [11/Jun/2025 12:35:10] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 12:35:10,648 INFO: 1******** - - [11/Jun/2025 12:35:10] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:36:02,473 INFO: 1******** - - [11/Jun/2025 12:36:02] "GET /app/3 HTTP/1.1" 200 -
2025-06-11 12:36:02,518 INFO: 1******** - - [11/Jun/2025 12:36:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:36:02,519 INFO: 1******** - - [11/Jun/2025 12:36:02] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 12:36:02,604 INFO: 1******** - - [11/Jun/2025 12:36:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:36:46,905 INFO: 1******** - - [11/Jun/2025 12:36:46] "GET /login HTTP/1.1" 200 -
2025-06-11 12:36:46,954 INFO: 1******** - - [11/Jun/2025 12:36:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:36:46,959 INFO: 1******** - - [11/Jun/2025 12:36:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:36:51,366 INFO: 1******** - - [11/Jun/2025 12:36:51] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 12:36:51,391 INFO: 1******** - - [11/Jun/2025 12:36:51] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:36:51,447 INFO: 1******** - - [11/Jun/2025 12:36:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:36:51,449 INFO: 1******** - - [11/Jun/2025 12:36:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:36:56,621 INFO: 1******** - - [11/Jun/2025 12:36:56] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 12:36:56,685 INFO: 1******** - - [11/Jun/2025 12:36:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:36:56,685 INFO: 1******** - - [11/Jun/2025 12:36:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:36:59,134 INFO: 1******** - - [11/Jun/2025 12:36:59] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 12:36:59,189 INFO: 1******** - - [11/Jun/2025 12:36:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:36:59,190 INFO: 1******** - - [11/Jun/2025 12:36:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:37:00,390 INFO: 1******** - - [11/Jun/2025 12:37:00] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 12:37:00,445 INFO: 1******** - - [11/Jun/2025 12:37:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:37:00,446 INFO: 1******** - - [11/Jun/2025 12:37:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:37:03,549 INFO: 1******** - - [11/Jun/2025 12:37:03] "GET / HTTP/1.1" 200 -
2025-06-11 12:37:03,599 INFO: 1******** - - [11/Jun/2025 12:37:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:37:03,600 INFO: 1******** - - [11/Jun/2025 12:37:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:37:05,417 INFO: 1******** - - [11/Jun/2025 12:37:05] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:37:05,481 INFO: 1******** - - [11/Jun/2025 12:37:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:37:05,481 INFO: 1******** - - [11/Jun/2025 12:37:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:37:07,011 INFO: 1******** - - [11/Jun/2025 12:37:07] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 12:37:07,091 INFO: 1******** - - [11/Jun/2025 12:37:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:37:07,095 INFO: 1******** - - [11/Jun/2025 12:37:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:37:25,606 INFO: 1******** - - [11/Jun/2025 12:37:25] "POST /admin/shortlinks/create HTTP/1.1" 200 -
2025-06-11 12:37:26,790 INFO: 1******** - - [11/Jun/2025 12:37:26] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 12:37:26,835 INFO: 1******** - - [11/Jun/2025 12:37:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:37:26,836 INFO: 1******** - - [11/Jun/2025 12:37:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:37:26,931 INFO: 1******** - - [11/Jun/2025 12:37:26] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:37:31,643 INFO: 1******** - - [11/Jun/2025 12:37:31] "[33mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj/ HTTP/1.1[0m" 404 -
2025-06-11 12:37:31,735 INFO: 1******** - - [11/Jun/2025 12:37:31] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 12:37:33,386 INFO: 1******** - - [11/Jun/2025 12:37:33] "[33mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj/ HTTP/1.1[0m" 404 -
2025-06-11 12:37:37,248 INFO: 1******** - - [11/Jun/2025 12:37:37] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 12:37:37,288 INFO: 1******** - - [11/Jun/2025 12:37:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:37:37,289 INFO: 1******** - - [11/Jun/2025 12:37:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:37:37,354 INFO: 1******** - - [11/Jun/2025 12:37:37] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:38:17,975 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 12:38:18,123 INFO:  * Restarting with stat
2025-06-11 12:38:19,016 WARNING:  * Debugger is active!
2025-06-11 12:38:19,024 INFO:  * Debugger PIN: 122-053-548
2025-06-11 12:38:37,814 INFO: 1******** - - [11/Jun/2025 12:38:37] "[33mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 404 -
2025-06-11 12:38:45,296 INFO: 1******** - - [11/Jun/2025 12:38:45] "[33mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 404 -
2025-06-11 12:38:49,131 INFO: 1******** - - [11/Jun/2025 12:38:49] "[32mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 302 -
2025-06-11 12:38:56,625 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 12:38:56,706 INFO:  * Restarting with stat
2025-06-11 12:38:57,362 WARNING:  * Debugger is active!
2025-06-11 12:38:57,373 INFO:  * Debugger PIN: 122-053-548
2025-06-11 12:39:31,503 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 12:39:31,613 INFO:  * Restarting with stat
2025-06-11 12:39:32,218 WARNING:  * Debugger is active!
2025-06-11 12:39:32,236 INFO:  * Debugger PIN: 122-053-548
2025-06-11 12:39:54,837 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 12:39:54,936 INFO:  * Restarting with stat
2025-06-11 12:39:55,476 WARNING:  * Debugger is active!
2025-06-11 12:39:55,488 INFO:  * Debugger PIN: 122-053-548
2025-06-11 12:42:49,085 INFO: 1******** - - [11/Jun/2025 12:42:49] "GET / HTTP/1.1" 200 -
2025-06-11 12:42:49,228 INFO: 1******** - - [11/Jun/2025 12:42:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:42:49,229 INFO: 1******** - - [11/Jun/2025 12:42:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:42:55,809 INFO: 1******** - - [11/Jun/2025 12:42:55] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:42:55,849 INFO: 1******** - - [11/Jun/2025 12:42:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:42:55,850 INFO: 1******** - - [11/Jun/2025 12:42:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:42:59,003 INFO: 1******** - - [11/Jun/2025 12:42:59] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 12:42:59,046 INFO: 1******** - - [11/Jun/2025 12:42:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:42:59,046 INFO: 1******** - - [11/Jun/2025 12:42:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:42:59,835 INFO: 1******** - - [11/Jun/2025 12:42:59] "[32mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 302 -
2025-06-11 12:43:10,796 INFO: 1******** - - [11/Jun/2025 12:43:10] "[35m[1mPOST /admin/shortlinks/toggle/1 HTTP/1.1[0m" 500 -
2025-06-11 12:43:14,892 INFO: 1******** - - [11/Jun/2025 12:43:14] "[35m[1mPOST /admin/shortlinks/toggle/1 HTTP/1.1[0m" 500 -
2025-06-11 12:43:16,634 INFO: 1******** - - [11/Jun/2025 12:43:16] "GET /admin HTTP/1.1" 200 -
2025-06-11 12:43:16,711 INFO: 1******** - - [11/Jun/2025 12:43:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:16,711 INFO: 1******** - - [11/Jun/2025 12:43:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:19,328 INFO: 1******** - - [11/Jun/2025 12:43:19] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 12:43:19,372 INFO: 1******** - - [11/Jun/2025 12:43:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:19,373 INFO: 1******** - - [11/Jun/2025 12:43:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:21,268 INFO: 1******** - - [11/Jun/2025 12:43:21] "POST /admin/toggle_user_status/2 HTTP/1.1" 200 -
2025-06-11 12:43:22,592 INFO: 1******** - - [11/Jun/2025 12:43:22] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 12:43:22,647 INFO: 1******** - - [11/Jun/2025 12:43:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:22,648 INFO: 1******** - - [11/Jun/2025 12:43:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:22,732 INFO: 1******** - - [11/Jun/2025 12:43:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:43:24,429 INFO: 1******** - - [11/Jun/2025 12:43:24] "POST /admin/toggle_user_status/2 HTTP/1.1" 200 -
2025-06-11 12:43:25,176 INFO: 1******** - - [11/Jun/2025 12:43:25] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 12:43:25,213 INFO: 1******** - - [11/Jun/2025 12:43:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:25,214 INFO: 1******** - - [11/Jun/2025 12:43:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:25,310 INFO: 1******** - - [11/Jun/2025 12:43:25] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 12:43:27,597 INFO: 1******** - - [11/Jun/2025 12:43:27] "GET / HTTP/1.1" 200 -
2025-06-11 12:43:27,643 INFO: 1******** - - [11/Jun/2025 12:43:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:27,645 INFO: 1******** - - [11/Jun/2025 12:43:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:29,242 INFO: 1******** - - [11/Jun/2025 12:43:29] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 12:43:29,292 INFO: 1******** - - [11/Jun/2025 12:43:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:29,292 INFO: 1******** - - [11/Jun/2025 12:43:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:32,257 INFO: 1******** - - [11/Jun/2025 12:43:32] "[32mPOST /app/1/rate HTTP/1.1[0m" 302 -
2025-06-11 12:43:32,274 INFO: 1******** - - [11/Jun/2025 12:43:32] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 12:43:32,311 INFO: 1******** - - [11/Jun/2025 12:43:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:32,311 INFO: 1******** - - [11/Jun/2025 12:43:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:36,235 INFO: 1******** - - [11/Jun/2025 12:43:36] "[32mPOST /app/1/rate HTTP/1.1[0m" 302 -
2025-06-11 12:43:36,249 INFO: 1******** - - [11/Jun/2025 12:43:36] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 12:43:36,312 INFO: 1******** - - [11/Jun/2025 12:43:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:36,313 INFO: 1******** - - [11/Jun/2025 12:43:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:44,184 INFO: 1******** - - [11/Jun/2025 12:43:44] "[32mPOST /app/1/rate HTTP/1.1[0m" 302 -
2025-06-11 12:43:44,204 INFO: 1******** - - [11/Jun/2025 12:43:44] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 12:43:44,250 INFO: 1******** - - [11/Jun/2025 12:43:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 12:43:44,252 INFO: 1******** - - [11/Jun/2025 12:43:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 12:43:46,698 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 12:43:46,753 INFO:  * Restarting with stat
2025-06-11 13:03:05,450 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:03:05,451 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:03:05,457 INFO:  * Restarting with stat
2025-06-11 13:03:06,293 WARNING:  * Debugger is active!
2025-06-11 13:03:06,300 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:03:06,860 INFO: 1******** - - [11/Jun/2025 13:03:06] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 13:03:06,891 INFO: 1******** - - [11/Jun/2025 13:03:06] "GET / HTTP/1.1" 200 -
2025-06-11 13:03:07,027 INFO: 1******** - - [11/Jun/2025 13:03:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:03:07,028 INFO: 1******** - - [11/Jun/2025 13:03:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:03:09,063 INFO: 1******** - - [11/Jun/2025 13:03:09] "GET / HTTP/1.1" 200 -
2025-06-11 13:03:09,095 INFO: 1******** - - [11/Jun/2025 13:03:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:03:09,096 INFO: 1******** - - [11/Jun/2025 13:03:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:03:10,317 INFO: 1******** - - [11/Jun/2025 13:03:10] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:03:11,427 INFO: 1******** - - [11/Jun/2025 13:03:11] "GET /app/14 HTTP/1.1" 200 -
2025-06-11 13:03:11,480 INFO: 1******** - - [11/Jun/2025 13:03:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:03:11,480 INFO: 1******** - - [11/Jun/2025 13:03:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:03:13,837 INFO: 1******** - - [11/Jun/2025 13:03:13] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:03:13,872 INFO: 1******** - - [11/Jun/2025 13:03:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:03:13,872 INFO: 1******** - - [11/Jun/2025 13:03:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:03:18,088 INFO: 1******** - - [11/Jun/2025 13:03:18] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 13:03:18,097 INFO: 1******** - - [11/Jun/2025 13:03:18] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:03:18,109 INFO: 1******** - - [11/Jun/2025 13:03:18] "GET / HTTP/1.1" 200 -
2025-06-11 13:03:18,140 INFO: 1******** - - [11/Jun/2025 13:03:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:03:18,143 INFO: 1******** - - [11/Jun/2025 13:03:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:05:33,090 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:05:33,091 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:05:33,095 INFO:  * Restarting with stat
2025-06-11 13:05:33,512 WARNING:  * Debugger is active!
2025-06-11 13:05:33,519 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:05:33,623 INFO: 1******** - - [11/Jun/2025 13:05:33] "GET / HTTP/1.1" 200 -
2025-06-11 13:05:33,661 INFO: 1******** - - [11/Jun/2025 13:05:33] "GET / HTTP/1.1" 200 -
2025-06-11 13:05:33,839 INFO: 1******** - - [11/Jun/2025 13:05:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:05:33,840 INFO: 1******** - - [11/Jun/2025 13:05:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:05:33,864 INFO: 1******** - - [11/Jun/2025 13:05:33] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 13:05:33,866 INFO: 1******** - - [11/Jun/2025 13:05:33] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 13:05:34,050 INFO: 1******** - - [11/Jun/2025 13:05:34] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:05:34,627 INFO: 1******** - - [11/Jun/2025 13:05:34] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:05:34,653 INFO: 1******** - - [11/Jun/2025 13:05:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:05:34,654 INFO: 1******** - - [11/Jun/2025 13:05:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:05:38,492 INFO: 1******** - - [11/Jun/2025 13:05:38] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 13:05:38,499 INFO: 1******** - - [11/Jun/2025 13:05:38] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:05:38,511 INFO: 1******** - - [11/Jun/2025 13:05:38] "GET / HTTP/1.1" 200 -
2025-06-11 13:05:38,544 INFO: 1******** - - [11/Jun/2025 13:05:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:05:38,545 INFO: 1******** - - [11/Jun/2025 13:05:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:05:45,418 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:05:45,419 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:05:45,423 INFO:  * Restarting with stat
2025-06-11 13:05:46,014 WARNING:  * Debugger is active!
2025-06-11 13:05:46,029 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:06:06,951 INFO: 1******** - - [11/Jun/2025 13:06:06] "GET / HTTP/1.1" 200 -
2025-06-11 13:06:07,098 INFO: 1******** - - [11/Jun/2025 13:06:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:06:07,102 INFO: 1******** - - [11/Jun/2025 13:06:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:06:07,186 INFO: 1******** - - [11/Jun/2025 13:06:07] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:06:07,743 INFO: 1******** - - [11/Jun/2025 13:06:07] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:06:07,784 INFO: 1******** - - [11/Jun/2025 13:06:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:06:07,784 INFO: 1******** - - [11/Jun/2025 13:06:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:11:33,611 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:11:33,612 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:13:10,094 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://1********:5001
2025-06-11 13:13:10,094 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:13:10,097 INFO:  * Restarting with stat
2025-06-11 13:13:10,512 WARNING:  * Debugger is active!
2025-06-11 13:13:10,519 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:13:59,810 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:13:59,811 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:13:59,813 INFO:  * Restarting with stat
2025-06-11 13:14:00,224 WARNING:  * Debugger is active!
2025-06-11 13:14:00,233 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:14:01,391 INFO: 1******** - - [11/Jun/2025 13:14:01] "GET / HTTP/1.1" 200 -
2025-06-11 13:14:01,562 INFO: 1******** - - [11/Jun/2025 13:14:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:14:01,562 INFO: 1******** - - [11/Jun/2025 13:14:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:14:01,788 INFO: 1******** - - [11/Jun/2025 13:14:01] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:14:04,097 INFO: 1******** - - [11/Jun/2025 13:14:04] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 13:14:04,145 INFO: 1******** - - [11/Jun/2025 13:14:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:14:04,145 INFO: 1******** - - [11/Jun/2025 13:14:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:14:06,395 INFO: 1******** - - [11/Jun/2025 13:14:06] "[32mGET /suggestions HTTP/1.1[0m" 302 -
2025-06-11 13:14:06,408 INFO: 1******** - - [11/Jun/2025 13:14:06] "GET / HTTP/1.1" 200 -
2025-06-11 13:14:06,438 INFO: 1******** - - [11/Jun/2025 13:14:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:14:06,439 INFO: 1******** - - [11/Jun/2025 13:14:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:14:37,738 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\auth.py', reloading
2025-06-11 13:14:37,792 INFO:  * Restarting with stat
2025-06-11 13:14:38,202 WARNING:  * Debugger is active!
2025-06-11 13:14:38,209 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:17:18,791 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\test_login.py', reloading
2025-06-11 13:17:19,002 INFO:  * Restarting with stat
2025-06-11 13:17:19,504 WARNING:  * Debugger is active!
2025-06-11 13:17:19,511 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:17:49,379 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\test_login.py', reloading
2025-06-11 13:17:49,425 INFO:  * Restarting with stat
2025-06-11 13:17:49,852 WARNING:  * Debugger is active!
2025-06-11 13:17:49,860 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:17:56,408 INFO: 1******** - - [11/Jun/2025 13:17:56] "GET / HTTP/1.1" 200 -
2025-06-11 13:17:56,566 INFO: 1******** - - [11/Jun/2025 13:17:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:17:56,566 INFO: 1******** - - [11/Jun/2025 13:17:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:17:56,650 INFO: 1******** - - [11/Jun/2025 13:17:56] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:17:58,104 INFO: 1******** - - [11/Jun/2025 13:17:58] "GET /app/12 HTTP/1.1" 200 -
2025-06-11 13:17:58,180 INFO: 1******** - - [11/Jun/2025 13:17:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:17:58,181 INFO: 1******** - - [11/Jun/2025 13:17:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:00,042 INFO: 1******** - - [11/Jun/2025 13:18:00] "[32mGET /suggestions HTTP/1.1[0m" 302 -
2025-06-11 13:18:00,056 INFO: 1******** - - [11/Jun/2025 13:18:00] "GET / HTTP/1.1" 200 -
2025-06-11 13:18:00,133 INFO: 1******** - - [11/Jun/2025 13:18:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:18:00,134 INFO: 1******** - - [11/Jun/2025 13:18:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:01,679 INFO: 1******** - - [11/Jun/2025 13:18:01] "GET /app/14 HTTP/1.1" 200 -
2025-06-11 13:18:01,706 INFO: 1******** - - [11/Jun/2025 13:18:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:01,708 INFO: 1******** - - [11/Jun/2025 13:18:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:18:05,866 INFO: 1******** - - [11/Jun/2025 13:18:05] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:18:05,892 INFO: 1******** - - [11/Jun/2025 13:18:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:05,893 INFO: 1******** - - [11/Jun/2025 13:18:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:18:09,327 INFO: 1******** - - [11/Jun/2025 13:18:09] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 13:18:09,352 INFO: 1******** - - [11/Jun/2025 13:18:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:18:09,352 INFO: 1******** - - [11/Jun/2025 13:18:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:13,487 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\test_login.py', reloading
2025-06-11 13:18:13,533 INFO:  * Restarting with stat
2025-06-11 13:18:14,006 WARNING:  * Debugger is active!
2025-06-11 13:18:14,014 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:18:14,261 INFO: 1******** - - [11/Jun/2025 13:18:14] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 13:18:14,268 INFO: 1******** - - [11/Jun/2025 13:18:14] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:18:14,307 INFO: 1******** - - [11/Jun/2025 13:18:14] "GET / HTTP/1.1" 200 -
2025-06-11 13:18:14,439 INFO: 1******** - - [11/Jun/2025 13:18:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:14,442 INFO: 1******** - - [11/Jun/2025 13:18:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:18:47,581 INFO: 1******** - - [11/Jun/2025 13:18:47] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:18:47,597 INFO: 1******** - - [11/Jun/2025 13:18:47] "GET / HTTP/1.1" 200 -
2025-06-11 13:18:47,639 INFO: 1******** - - [11/Jun/2025 13:18:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:47,646 INFO: 1******** - - [11/Jun/2025 13:18:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:18:55,882 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:18:55,883 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:18:59,203 INFO: 1******** - - [11/Jun/2025 13:18:59] "GET / HTTP/1.1" 200 -
2025-06-11 13:18:59,349 INFO: 1******** - - [11/Jun/2025 13:18:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:18:59,350 INFO: 1******** - - [11/Jun/2025 13:18:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:18:59,436 INFO: 1******** - - [11/Jun/2025 13:18:59] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:19:00,323 INFO: 1******** - - [11/Jun/2025 13:19:00] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:19:00,355 INFO: 1******** - - [11/Jun/2025 13:19:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:19:00,355 INFO: 1******** - - [11/Jun/2025 13:19:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:19:03,317 INFO: 1******** - - [11/Jun/2025 13:19:03] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 13:19:03,331 INFO: 1******** - - [11/Jun/2025 13:19:03] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:19:03,355 INFO: 1******** - - [11/Jun/2025 13:19:03] "GET / HTTP/1.1" 200 -
2025-06-11 13:19:03,446 INFO: 1******** - - [11/Jun/2025 13:19:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:19:03,450 INFO: 1******** - - [11/Jun/2025 13:19:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:21:53,919 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:21:53,920 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:21:53,924 INFO:  * Restarting with stat
2025-06-11 13:21:54,344 WARNING:  * Debugger is active!
2025-06-11 13:21:54,352 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:21:54,426 INFO: 1******** - - [11/Jun/2025 13:21:54] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:21:54,427 INFO: 1******** - - [11/Jun/2025 13:21:54] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:21:54,445 INFO: 1******** - - [11/Jun/2025 13:21:54] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:21:54,679 INFO: 1******** - - [11/Jun/2025 13:21:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:21:54,680 INFO: 1******** - - [11/Jun/2025 13:21:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:21:54,807 INFO: 1******** - - [11/Jun/2025 13:21:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:22:04,831 INFO: 1******** - - [11/Jun/2025 13:22:04] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 13:22:04,841 INFO: 1******** - - [11/Jun/2025 13:22:04] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:22:04,872 INFO: 1******** - - [11/Jun/2025 13:22:04] "GET / HTTP/1.1" 200 -
2025-06-11 13:22:04,915 INFO: 1******** - - [11/Jun/2025 13:22:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:22:04,915 INFO: 1******** - - [11/Jun/2025 13:22:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:22:16,851 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 13:22:16,899 INFO:  * Restarting with stat
2025-06-11 13:22:17,466 WARNING:  * Debugger is active!
2025-06-11 13:22:17,473 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:22:33,614 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 13:22:33,666 INFO:  * Restarting with stat
2025-06-11 13:22:34,135 WARNING:  * Debugger is active!
2025-06-11 13:22:34,142 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:22:42,785 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 13:22:42,851 INFO:  * Restarting with stat
2025-06-11 13:22:43,288 WARNING:  * Debugger is active!
2025-06-11 13:22:43,297 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:25:41,766 INFO: 1******** - - [11/Jun/2025 13:25:41] "GET / HTTP/1.1" 200 -
2025-06-11 13:25:41,889 INFO: 1******** - - [11/Jun/2025 13:25:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:25:41,890 INFO: 1******** - - [11/Jun/2025 13:25:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:25:41,957 INFO: 1******** - - [11/Jun/2025 13:25:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:25:42,733 INFO: 1******** - - [11/Jun/2025 13:25:42] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:25:42,779 INFO: 1******** - - [11/Jun/2025 13:25:42] "GET / HTTP/1.1" 200 -
2025-06-11 13:25:42,810 INFO: 1******** - - [11/Jun/2025 13:25:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:25:42,850 INFO: 1******** - - [11/Jun/2025 13:25:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:28:05,810 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 13:28:05,872 INFO:  * Restarting with stat
2025-06-11 13:28:06,385 WARNING:  * Debugger is active!
2025-06-11 13:28:06,392 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:29:10,216 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 13:29:10,260 INFO:  * Restarting with stat
2025-06-11 13:29:10,786 WARNING:  * Debugger is active!
2025-06-11 13:29:10,794 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:30:24,442 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 13:30:24,519 INFO:  * Restarting with stat
2025-06-11 13:30:25,059 WARNING:  * Debugger is active!
2025-06-11 13:30:25,066 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:31:36,651 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:31:36,652 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:31:36,656 INFO:  * Restarting with stat
2025-06-11 13:31:37,189 WARNING:  * Debugger is active!
2025-06-11 13:31:37,199 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:31:38,098 INFO: 1******** - - [11/Jun/2025 13:31:38] "GET / HTTP/1.1" 200 -
2025-06-11 13:31:38,285 INFO: 1******** - - [11/Jun/2025 13:31:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:31:38,287 INFO: 1******** - - [11/Jun/2025 13:31:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:31:38,352 INFO: 1******** - - [11/Jun/2025 13:31:38] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:31:39,020 INFO: 1******** - - [11/Jun/2025 13:31:39] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:31:39,080 INFO: 1******** - - [11/Jun/2025 13:31:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:31:39,081 INFO: 1******** - - [11/Jun/2025 13:31:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:31:42,900 INFO: 1******** - - [11/Jun/2025 13:31:42] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 13:31:42,909 INFO: 1******** - - [11/Jun/2025 13:31:42] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:31:42,920 INFO: 1******** - - [11/Jun/2025 13:31:42] "GET / HTTP/1.1" 200 -
2025-06-11 13:31:42,994 INFO: 1******** - - [11/Jun/2025 13:31:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:31:42,994 INFO: 1******** - - [11/Jun/2025 13:31:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:32:41,151 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 13:32:41,217 INFO:  * Restarting with stat
2025-06-11 13:32:41,646 WARNING:  * Debugger is active!
2025-06-11 13:32:41,653 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:35:25,055 INFO: 1******** - - [11/Jun/2025 13:35:25] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 13:35:25,182 INFO: 1******** - - [11/Jun/2025 13:35:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:35:25,184 INFO: 1******** - - [11/Jun/2025 13:35:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:35:28,655 INFO: 1******** - - [11/Jun/2025 13:35:28] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 13:35:28,839 INFO: 1******** - - [11/Jun/2025 13:35:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:35:28,839 INFO: 1******** - - [11/Jun/2025 13:35:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:35:30,915 INFO: 1******** - - [11/Jun/2025 13:35:30] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 13:35:30,976 INFO: 1******** - - [11/Jun/2025 13:35:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:35:30,976 INFO: 1******** - - [11/Jun/2025 13:35:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:35:32,099 INFO: 1******** - - [11/Jun/2025 13:35:32] "[32mGET /admin/logs HTTP/1.1[0m" 302 -
2025-06-11 13:35:32,111 INFO: 1******** - - [11/Jun/2025 13:35:32] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 13:35:32,146 INFO: 1******** - - [11/Jun/2025 13:35:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:35:32,147 INFO: 1******** - - [11/Jun/2025 13:35:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:35:36,554 INFO: 1******** - - [11/Jun/2025 13:35:36] "[32mGET /admin/reports HTTP/1.1[0m" 302 -
2025-06-11 13:35:36,583 INFO: 1******** - - [11/Jun/2025 13:35:36] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 13:35:36,612 INFO: 1******** - - [11/Jun/2025 13:35:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:35:36,613 INFO: 1******** - - [11/Jun/2025 13:35:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:35:38,836 INFO: 1******** - - [11/Jun/2025 13:35:38] "[32mGET /admin/shortlinks HTTP/1.1[0m" 302 -
2025-06-11 13:35:38,849 INFO: 1******** - - [11/Jun/2025 13:35:38] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 13:35:38,874 INFO: 1******** - - [11/Jun/2025 13:35:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:35:38,875 INFO: 1******** - - [11/Jun/2025 13:35:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:35:40,324 INFO: 1******** - - [11/Jun/2025 13:35:40] "GET / HTTP/1.1" 200 -
2025-06-11 13:35:40,358 INFO: 1******** - - [11/Jun/2025 13:35:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:35:40,358 INFO: 1******** - - [11/Jun/2025 13:35:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:16,041 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 13:49:16,042 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 13:49:16,045 INFO:  * Restarting with stat
2025-06-11 13:49:16,509 WARNING:  * Debugger is active!
2025-06-11 13:49:16,516 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:49:17,207 INFO: 1******** - - [11/Jun/2025 13:49:17] "[32mGET /admin/dashboard HTTP/1.1[0m" 302 -
2025-06-11 13:49:17,225 INFO: 1******** - - [11/Jun/2025 13:49:17] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 13:49:17,367 INFO: 1******** - - [11/Jun/2025 13:49:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:17,368 INFO: 1******** - - [11/Jun/2025 13:49:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:17,424 INFO: 1******** - - [11/Jun/2025 13:49:17] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:49:21,461 INFO: 1******** - - [11/Jun/2025 13:49:21] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 13:49:21,487 INFO: 1******** - - [11/Jun/2025 13:49:21] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 13:49:21,557 INFO: 1******** - - [11/Jun/2025 13:49:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:21,557 INFO: 1******** - - [11/Jun/2025 13:49:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:23,550 INFO: 1******** - - [11/Jun/2025 13:49:23] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 13:49:23,579 INFO: 1******** - - [11/Jun/2025 13:49:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:23,579 INFO: 1******** - - [11/Jun/2025 13:49:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:24,808 INFO: 1******** - - [11/Jun/2025 13:49:24] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 13:49:24,833 INFO: 1******** - - [11/Jun/2025 13:49:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:24,833 INFO: 1******** - - [11/Jun/2025 13:49:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:26,386 INFO: 1******** - - [11/Jun/2025 13:49:26] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 13:49:26,440 INFO: 1******** - - [11/Jun/2025 13:49:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:26,457 INFO: 1******** - - [11/Jun/2025 13:49:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:26,970 INFO: 1******** - - [11/Jun/2025 13:49:26] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 13:49:26,996 INFO: 1******** - - [11/Jun/2025 13:49:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:26,997 INFO: 1******** - - [11/Jun/2025 13:49:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:27,416 INFO: 1******** - - [11/Jun/2025 13:49:27] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 13:49:27,449 INFO: 1******** - - [11/Jun/2025 13:49:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:27,450 INFO: 1******** - - [11/Jun/2025 13:49:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:27,881 INFO: 1******** - - [11/Jun/2025 13:49:27] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 13:49:27,914 INFO: 1******** - - [11/Jun/2025 13:49:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:27,914 INFO: 1******** - - [11/Jun/2025 13:49:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:31,048 INFO: 1******** - - [11/Jun/2025 13:49:31] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 13:49:31,099 INFO: 1******** - - [11/Jun/2025 13:49:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:31,100 INFO: 1******** - - [11/Jun/2025 13:49:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:32,459 INFO: 1******** - - [11/Jun/2025 13:49:32] "GET /admin/users/add HTTP/1.1" 200 -
2025-06-11 13:49:32,489 INFO: 1******** - - [11/Jun/2025 13:49:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:32,490 INFO: 1******** - - [11/Jun/2025 13:49:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:33,491 INFO: 1******** - - [11/Jun/2025 13:49:33] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 13:49:33,519 INFO: 1******** - - [11/Jun/2025 13:49:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:33,520 INFO: 1******** - - [11/Jun/2025 13:49:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:34,359 INFO: 1******** - - [11/Jun/2025 13:49:34] "GET /admin/apps/add HTTP/1.1" 200 -
2025-06-11 13:49:34,399 INFO: 1******** - - [11/Jun/2025 13:49:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:34,399 INFO: 1******** - - [11/Jun/2025 13:49:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:35,411 INFO: 1******** - - [11/Jun/2025 13:49:35] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 13:49:35,438 INFO: 1******** - - [11/Jun/2025 13:49:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:49:35,439 INFO: 1******** - - [11/Jun/2025 13:49:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:36,353 INFO: 1******** - - [11/Jun/2025 13:49:36] "GET / HTTP/1.1" 200 -
2025-06-11 13:49:36,380 INFO: 1******** - - [11/Jun/2025 13:49:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:49:36,380 INFO: 1******** - - [11/Jun/2025 13:49:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:53:38,583 INFO: 1******** - - [11/Jun/2025 13:53:38] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 13:53:38,721 INFO: 1******** - - [11/Jun/2025 13:53:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:53:38,722 INFO: 1******** - - [11/Jun/2025 13:53:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:53:40,235 INFO: 1******** - - [11/Jun/2025 13:53:40] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 13:53:40,316 INFO: 1******** - - [11/Jun/2025 13:53:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:53:40,316 INFO: 1******** - - [11/Jun/2025 13:53:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:53:41,434 INFO: 1******** - - [11/Jun/2025 13:53:41] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 13:53:41,461 INFO: 1******** - - [11/Jun/2025 13:53:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:53:41,461 INFO: 1******** - - [11/Jun/2025 13:53:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:53:42,264 INFO: 1******** - - [11/Jun/2025 13:53:42] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 13:53:42,294 INFO: 1******** - - [11/Jun/2025 13:53:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:53:42,294 INFO: 1******** - - [11/Jun/2025 13:53:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:53:42,704 INFO: 1******** - - [11/Jun/2025 13:53:42] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 13:53:42,728 INFO: 1******** - - [11/Jun/2025 13:53:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:53:42,735 INFO: 1******** - - [11/Jun/2025 13:53:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:53:43,577 INFO: 1******** - - [11/Jun/2025 13:53:43] "GET / HTTP/1.1" 200 -
2025-06-11 13:53:43,604 INFO: 1******** - - [11/Jun/2025 13:53:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:53:43,605 INFO: 1******** - - [11/Jun/2025 13:53:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:53:46,716 INFO: 1******** - - [11/Jun/2025 13:53:46] "GET / HTTP/1.1" 200 -
2025-06-11 13:53:53,071 INFO: 1******** - - [11/Jun/2025 13:53:53] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 13:55:48,452 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\check_db.py', reloading
2025-06-11 13:55:48,501 INFO:  * Restarting with stat
2025-06-11 13:55:48,901 WARNING:  * Debugger is active!
2025-06-11 13:55:48,908 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:56:46,361 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\check_db.py', reloading
2025-06-11 13:56:46,401 INFO:  * Restarting with stat
2025-06-11 13:56:46,812 WARNING:  * Debugger is active!
2025-06-11 13:56:46,819 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:57:37,839 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\check_db.py', reloading
2025-06-11 13:57:37,880 INFO:  * Restarting with stat
2025-06-11 13:57:38,282 WARNING:  * Debugger is active!
2025-06-11 13:57:38,289 INFO:  * Debugger PIN: 122-053-548
2025-06-11 13:59:00,105 INFO: 1******** - - [11/Jun/2025 13:59:00] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 13:59:00,267 INFO: 1******** - - [11/Jun/2025 13:59:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:59:00,268 INFO: 1******** - - [11/Jun/2025 13:59:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:59:02,322 INFO: 1******** - - [11/Jun/2025 13:59:02] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 13:59:02,386 INFO: 1******** - - [11/Jun/2025 13:59:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:59:02,386 INFO: 1******** - - [11/Jun/2025 13:59:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:59:03,536 INFO: 1******** - - [11/Jun/2025 13:59:03] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 13:59:03,570 INFO: 1******** - - [11/Jun/2025 13:59:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:59:03,571 INFO: 1******** - - [11/Jun/2025 13:59:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 13:59:05,512 INFO: 1******** - - [11/Jun/2025 13:59:05] "GET / HTTP/1.1" 200 -
2025-06-11 13:59:05,549 INFO: 1******** - - [11/Jun/2025 13:59:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 13:59:05,550 INFO: 1******** - - [11/Jun/2025 13:59:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:08:57,969 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 14:08:57,970 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 14:08:57,972 INFO:  * Restarting with stat
2025-06-11 14:08:58,372 WARNING:  * Debugger is active!
2025-06-11 14:08:58,378 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:08:59,500 INFO: 1******** - - [11/Jun/2025 14:08:59] "GET / HTTP/1.1" 200 -
2025-06-11 14:08:59,637 INFO: 1******** - - [11/Jun/2025 14:08:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:08:59,637 INFO: 1******** - - [11/Jun/2025 14:08:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:00,295 INFO: 1******** - - [11/Jun/2025 14:09:00] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:09:00,340 INFO: 1******** - - [11/Jun/2025 14:09:00] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 14:09:00,387 INFO: 1******** - - [11/Jun/2025 14:09:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:09:00,387 INFO: 1******** - - [11/Jun/2025 14:09:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:04,108 INFO: 1******** - - [11/Jun/2025 14:09:04] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 14:09:04,134 INFO: 1******** - - [11/Jun/2025 14:09:04] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 14:09:04,166 INFO: 1******** - - [11/Jun/2025 14:09:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:04,167 INFO: 1******** - - [11/Jun/2025 14:09:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:09:06,928 INFO: 1******** - - [11/Jun/2025 14:09:06] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 14:09:06,962 INFO: 1******** - - [11/Jun/2025 14:09:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:09:06,962 INFO: 1******** - - [11/Jun/2025 14:09:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:07,760 INFO: 1******** - - [11/Jun/2025 14:09:07] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 14:09:07,790 INFO: 1******** - - [11/Jun/2025 14:09:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:09:07,790 INFO: 1******** - - [11/Jun/2025 14:09:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:08,100 INFO: 1******** - - [11/Jun/2025 14:09:08] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 14:09:08,128 INFO: 1******** - - [11/Jun/2025 14:09:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:08,128 INFO: 1******** - - [11/Jun/2025 14:09:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:09:08,778 INFO: 1******** - - [11/Jun/2025 14:09:08] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 14:09:08,807 INFO: 1******** - - [11/Jun/2025 14:09:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:09:08,808 INFO: 1******** - - [11/Jun/2025 14:09:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:09,346 INFO: 1******** - - [11/Jun/2025 14:09:09] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 14:09:09,375 INFO: 1******** - - [11/Jun/2025 14:09:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:09,375 INFO: 1******** - - [11/Jun/2025 14:09:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:09:10,024 INFO: 1******** - - [11/Jun/2025 14:09:10] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 14:09:10,053 INFO: 1******** - - [11/Jun/2025 14:09:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:09:10,055 INFO: 1******** - - [11/Jun/2025 14:09:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:10:21,435 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 14:10:21,476 INFO:  * Restarting with stat
2025-06-11 14:10:21,897 WARNING:  * Debugger is active!
2025-06-11 14:10:21,904 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:11:49,017 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 14:11:49,101 INFO:  * Restarting with stat
2025-06-11 14:11:49,648 WARNING:  * Debugger is active!
2025-06-11 14:11:49,656 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:12:11,047 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 14:12:11,129 INFO:  * Restarting with stat
2025-06-11 14:12:11,537 WARNING:  * Debugger is active!
2025-06-11 14:12:11,544 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:12:33,962 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 14:12:34,016 INFO:  * Restarting with stat
2025-06-11 14:12:34,645 WARNING:  * Debugger is active!
2025-06-11 14:12:34,652 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:15:24,500 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 14:15:24,590 INFO:  * Restarting with stat
2025-06-11 14:15:25,092 WARNING:  * Debugger is active!
2025-06-11 14:15:25,100 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:15:52,285 INFO: 1******** - - [11/Jun/2025 14:15:52] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 14:15:52,401 INFO: 1******** - - [11/Jun/2025 14:15:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:15:52,402 INFO: 1******** - - [11/Jun/2025 14:15:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:15:52,477 INFO: 1******** - - [11/Jun/2025 14:15:52] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:15:54,580 INFO: 1******** - - [11/Jun/2025 14:15:54] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 14:15:54,660 INFO: 1******** - - [11/Jun/2025 14:15:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:15:54,661 INFO: 1******** - - [11/Jun/2025 14:15:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:15:55,907 INFO: 1******** - - [11/Jun/2025 14:15:55] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 14:15:55,942 INFO: 1******** - - [11/Jun/2025 14:15:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:15:55,942 INFO: 1******** - - [11/Jun/2025 14:15:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:15:56,764 INFO: 1******** - - [11/Jun/2025 14:15:56] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 14:15:56,791 INFO: 1******** - - [11/Jun/2025 14:15:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:15:56,793 INFO: 1******** - - [11/Jun/2025 14:15:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:15:59,530 INFO: 1******** - - [11/Jun/2025 14:15:59] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 14:15:59,566 INFO: 1******** - - [11/Jun/2025 14:15:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:15:59,567 INFO: 1******** - - [11/Jun/2025 14:15:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:02,250 INFO: 1******** - - [11/Jun/2025 14:16:02] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 14:16:02,282 INFO: 1******** - - [11/Jun/2025 14:16:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:02,283 INFO: 1******** - - [11/Jun/2025 14:16:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:06,237 INFO: 1******** - - [11/Jun/2025 14:16:06] "GET /admin/logs?type=download&page=9 HTTP/1.1" 200 -
2025-06-11 14:16:06,276 INFO: 1******** - - [11/Jun/2025 14:16:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:06,277 INFO: 1******** - - [11/Jun/2025 14:16:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:09,574 INFO: 1******** - - [11/Jun/2025 14:16:09] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 14:16:09,610 INFO: 1******** - - [11/Jun/2025 14:16:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:09,611 INFO: 1******** - - [11/Jun/2025 14:16:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:12,539 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 14:16:12,611 INFO:  * Restarting with stat
2025-06-11 14:16:13,186 WARNING:  * Debugger is active!
2025-06-11 14:16:13,195 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:16:18,086 INFO: 1******** - - [11/Jun/2025 14:16:18] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:16:18,208 INFO: 1******** - - [11/Jun/2025 14:16:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:18,209 INFO: 1******** - - [11/Jun/2025 14:16:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:20,779 INFO: 1******** - - [11/Jun/2025 14:16:20] "[33mPOST /admin/toggle_user_status/2 HTTP/1.1[0m" 404 -
2025-06-11 14:16:29,560 INFO: 1******** - - [11/Jun/2025 14:16:29] "[33mGET /admin/edit_user/2 HTTP/1.1[0m" 404 -
2025-06-11 14:16:37,270 INFO: 1******** - - [11/Jun/2025 14:16:37] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 14:16:37,305 INFO: 1******** - - [11/Jun/2025 14:16:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:37,306 INFO: 1******** - - [11/Jun/2025 14:16:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:38,617 INFO: 1******** - - [11/Jun/2025 14:16:38] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 14:16:38,656 INFO: 1******** - - [11/Jun/2025 14:16:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:38,657 INFO: 1******** - - [11/Jun/2025 14:16:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:39,499 INFO: 1******** - - [11/Jun/2025 14:16:39] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 14:16:39,529 INFO: 1******** - - [11/Jun/2025 14:16:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:39,529 INFO: 1******** - - [11/Jun/2025 14:16:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:51,239 INFO: 1******** - - [11/Jun/2025 14:16:51] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 14:16:51,276 INFO: 1******** - - [11/Jun/2025 14:16:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:16:51,277 INFO: 1******** - - [11/Jun/2025 14:16:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:16:55,090 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 14:16:55,174 INFO:  * Restarting with stat
2025-06-11 14:16:55,643 WARNING:  * Debugger is active!
2025-06-11 14:16:55,650 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:17:20,529 INFO: 1******** - - [11/Jun/2025 14:17:20] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 14:17:21,535 INFO: 1******** - - [11/Jun/2025 14:17:21] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 14:17:22,227 INFO: 1******** - - [11/Jun/2025 14:17:22] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 14:17:23,339 INFO: 1******** - - [11/Jun/2025 14:17:23] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:17:23,463 INFO: 1******** - - [11/Jun/2025 14:17:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:17:23,465 INFO: 1******** - - [11/Jun/2025 14:17:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:17:26,149 INFO: 1******** - - [11/Jun/2025 14:17:26] "POST /admin/shortlinks/toggle/1 HTTP/1.1" 200 -
2025-06-11 14:17:26,933 INFO: 1******** - - [11/Jun/2025 14:17:26] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:17:27,057 INFO: 1******** - - [11/Jun/2025 14:17:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:17:27,058 INFO: 1******** - - [11/Jun/2025 14:17:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:17:27,127 INFO: 1******** - - [11/Jun/2025 14:17:27] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:17:37,265 INFO: 1******** - - [11/Jun/2025 14:17:37] "[33mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 404 -
2025-06-11 14:17:39,254 INFO: 1******** - - [11/Jun/2025 14:17:39] "[33mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 404 -
2025-06-11 14:17:40,605 INFO: 1******** - - [11/Jun/2025 14:17:40] "[33mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 404 -
2025-06-11 14:17:45,429 INFO: 1******** - - [11/Jun/2025 14:17:45] "POST /admin/shortlinks/toggle/1 HTTP/1.1" 200 -
2025-06-11 14:17:46,334 INFO: 1******** - - [11/Jun/2025 14:17:46] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:17:46,367 INFO: 1******** - - [11/Jun/2025 14:17:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:17:46,369 INFO: 1******** - - [11/Jun/2025 14:17:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:17:46,419 INFO: 1******** - - [11/Jun/2025 14:17:46] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:17:49,259 INFO: 1******** - - [11/Jun/2025 14:17:49] "[32mGET /s/fdsgfgdfs4255425ldfjgkdfjgklfjsgkldjfsgj HTTP/1.1[0m" 302 -
2025-06-11 14:17:53,514 INFO: 1******** - - [11/Jun/2025 14:17:53] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:17:53,548 INFO: 1******** - - [11/Jun/2025 14:17:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:17:53,549 INFO: 1******** - - [11/Jun/2025 14:17:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:17:53,600 INFO: 1******** - - [11/Jun/2025 14:17:53] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:17:59,082 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\final_test.py', reloading
2025-06-11 14:17:59,140 INFO:  * Restarting with stat
2025-06-11 14:17:59,569 WARNING:  * Debugger is active!
2025-06-11 14:17:59,577 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:17:59,680 INFO: 1******** - - [11/Jun/2025 14:17:59] "POST /admin/shortlinks/create HTTP/1.1" 200 -
2025-06-11 14:18:00,719 INFO: 1******** - - [11/Jun/2025 14:18:00] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:18:00,848 INFO: 1******** - - [11/Jun/2025 14:18:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:18:00,852 INFO: 1******** - - [11/Jun/2025 14:18:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:18:00,930 INFO: 1******** - - [11/Jun/2025 14:18:00] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:18:05,122 INFO: 1******** - - [11/Jun/2025 14:18:05] "POST /admin/shortlinks/delete/2 HTTP/1.1" 200 -
2025-06-11 14:18:06,176 INFO: 1******** - - [11/Jun/2025 14:18:06] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:18:06,221 INFO: 1******** - - [11/Jun/2025 14:18:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:18:06,221 INFO: 1******** - - [11/Jun/2025 14:18:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:18:06,291 INFO: 1******** - - [11/Jun/2025 14:18:06] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:18:09,172 INFO: 1******** - - [11/Jun/2025 14:18:09] "POST /admin/shortlinks/create HTTP/1.1" 200 -
2025-06-11 14:18:10,230 INFO: 1******** - - [11/Jun/2025 14:18:10] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:18:10,275 INFO: 1******** - - [11/Jun/2025 14:18:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:18:10,276 INFO: 1******** - - [11/Jun/2025 14:18:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:18:10,346 INFO: 1******** - - [11/Jun/2025 14:18:10] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:18:29,612 INFO: 1******** - - [11/Jun/2025 14:18:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:18:33,317 INFO: 1******** - - [11/Jun/2025 14:18:33] "POST /admin/shortlinks/delete/3 HTTP/1.1" 200 -
2025-06-11 14:18:34,314 INFO: 1******** - - [11/Jun/2025 14:18:34] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:18:34,359 INFO: 1******** - - [11/Jun/2025 14:18:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:18:34,360 INFO: 1******** - - [11/Jun/2025 14:18:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:18:34,610 INFO: 1******** - - [11/Jun/2025 14:18:34] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:18:37,837 INFO: 1******** - - [11/Jun/2025 14:18:37] "POST /admin/shortlinks/create HTTP/1.1" 200 -
2025-06-11 14:18:47,788 INFO: 1******** - - [11/Jun/2025 14:18:47] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:18:47,832 INFO: 1******** - - [11/Jun/2025 14:18:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:18:47,835 INFO: 1******** - - [11/Jun/2025 14:18:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:18:48,028 INFO: 1******** - - [11/Jun/2025 14:18:48] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:18:53,136 INFO: 1******** - - [11/Jun/2025 14:18:53] "POST /admin/shortlinks/delete/4 HTTP/1.1" 200 -
2025-06-11 14:18:55,984 INFO: 1******** - - [11/Jun/2025 14:18:55] "GET /admin/shortlinks HTTP/1.1" 200 -
2025-06-11 14:18:56,058 INFO: 1******** - - [11/Jun/2025 14:18:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:18:56,058 INFO: 1******** - - [11/Jun/2025 14:18:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:18:56,224 INFO: 1******** - - [11/Jun/2025 14:18:56] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:19:17,945 INFO: 1******** - - [11/Jun/2025 14:19:17] "GET / HTTP/1.1" 200 -
2025-06-11 14:19:17,983 INFO: 1******** - - [11/Jun/2025 14:19:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:19:17,984 INFO: 1******** - - [11/Jun/2025 14:19:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:19:23,882 INFO: 1******** - - [11/Jun/2025 14:19:23] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 14:19:23,921 INFO: 1******** - - [11/Jun/2025 14:19:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:19:23,921 INFO: 1******** - - [11/Jun/2025 14:19:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:19:29,688 INFO: 1******** - - [11/Jun/2025 14:19:29] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 14:19:29,745 INFO: 1******** - - [11/Jun/2025 14:19:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:19:29,746 INFO: 1******** - - [11/Jun/2025 14:19:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:19:30,994 INFO: 1******** - - [11/Jun/2025 14:19:30] "GET /admin/users/add HTTP/1.1" 200 -
2025-06-11 14:19:31,022 INFO: 1******** - - [11/Jun/2025 14:19:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:19:31,022 INFO: 1******** - - [11/Jun/2025 14:19:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:19:32,229 INFO: 1******** - - [11/Jun/2025 14:19:32] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 14:19:32,259 INFO: 1******** - - [11/Jun/2025 14:19:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:19:32,259 INFO: 1******** - - [11/Jun/2025 14:19:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:19:33,459 INFO: 1******** - - [11/Jun/2025 14:19:33] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 14:19:33,564 INFO: 1******** - - [11/Jun/2025 14:19:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:19:33,566 INFO: 1******** - - [11/Jun/2025 14:19:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:21:00,751 INFO: 1******** - - [11/Jun/2025 14:21:00] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 14:21:00,816 INFO: 1******** - - [11/Jun/2025 14:21:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:21:00,816 INFO: 1******** - - [11/Jun/2025 14:21:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:21:02,599 INFO: 1******** - - [11/Jun/2025 14:21:02] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 14:21:02,634 INFO: 1******** - - [11/Jun/2025 14:21:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:21:02,636 INFO: 1******** - - [11/Jun/2025 14:21:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:21:03,996 INFO: 1******** - - [11/Jun/2025 14:21:03] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 14:21:04,024 INFO: 1******** - - [11/Jun/2025 14:21:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:21:04,024 INFO: 1******** - - [11/Jun/2025 14:21:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:21:05,173 INFO: 1******** - - [11/Jun/2025 14:21:05] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:21:05,209 INFO: 1******** - - [11/Jun/2025 14:21:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:21:05,210 INFO: 1******** - - [11/Jun/2025 14:21:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:21:06,388 INFO: 1******** - - [11/Jun/2025 14:21:06] "[33mGET /admin/edit_user/2 HTTP/1.1[0m" 404 -
2025-06-11 14:21:45,004 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 14:21:45,047 INFO:  * Restarting with stat
2025-06-11 14:21:45,559 WARNING:  * Debugger is active!
2025-06-11 14:21:45,567 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:22:26,088 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 14:22:26,128 INFO:  * Restarting with stat
2025-06-11 14:22:26,613 WARNING:  * Debugger is active!
2025-06-11 14:22:26,621 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:27:54,754 INFO: 1******** - - [11/Jun/2025 14:27:54] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 14:27:54,867 INFO: 1******** - - [11/Jun/2025 14:27:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:27:54,868 INFO: 1******** - - [11/Jun/2025 14:27:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:27:58,543 INFO: 1******** - - [11/Jun/2025 14:27:58] "GET /app/5 HTTP/1.1" 200 -
2025-06-11 14:27:58,597 INFO: 1******** - - [11/Jun/2025 14:27:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:27:58,615 INFO: 1******** - - [11/Jun/2025 14:27:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:27:59,757 INFO: 1******** - - [11/Jun/2025 14:27:59] "GET /admin/apps/edit/5 HTTP/1.1" 200 -
2025-06-11 14:27:59,779 INFO: 1******** - - [11/Jun/2025 14:27:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:27:59,780 INFO: 1******** - - [11/Jun/2025 14:27:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:02,363 INFO: 1******** - - [11/Jun/2025 14:28:02] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:28:02,387 INFO: 1******** - - [11/Jun/2025 14:28:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:02,388 INFO: 1******** - - [11/Jun/2025 14:28:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:04,123 INFO: 1******** - - [11/Jun/2025 14:28:04] "POST /admin/users/toggle_status/2 HTTP/1.1" 200 -
2025-06-11 14:28:05,008 INFO: 1******** - - [11/Jun/2025 14:28:05] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:28:05,090 INFO: 1******** - - [11/Jun/2025 14:28:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:05,090 INFO: 1******** - - [11/Jun/2025 14:28:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:05,151 INFO: 1******** - - [11/Jun/2025 14:28:05] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:28:06,317 INFO: 1******** - - [11/Jun/2025 14:28:06] "POST /admin/users/toggle_status/2 HTTP/1.1" 200 -
2025-06-11 14:28:06,904 INFO: 1******** - - [11/Jun/2025 14:28:06] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:28:06,935 INFO: 1******** - - [11/Jun/2025 14:28:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:06,936 INFO: 1******** - - [11/Jun/2025 14:28:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:06,980 INFO: 1******** - - [11/Jun/2025 14:28:06] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 14:28:08,687 INFO: 1******** - - [11/Jun/2025 14:28:08] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 14:28:08,723 INFO: 1******** - - [11/Jun/2025 14:28:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:08,724 INFO: 1******** - - [11/Jun/2025 14:28:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:09,641 INFO: 1******** - - [11/Jun/2025 14:28:09] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 14:28:09,670 INFO: 1******** - - [11/Jun/2025 14:28:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:09,670 INFO: 1******** - - [11/Jun/2025 14:28:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:10,121 INFO: 1******** - - [11/Jun/2025 14:28:10] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 14:28:10,153 INFO: 1******** - - [11/Jun/2025 14:28:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:10,153 INFO: 1******** - - [11/Jun/2025 14:28:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:10,602 INFO: 1******** - - [11/Jun/2025 14:28:10] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 14:28:10,634 INFO: 1******** - - [11/Jun/2025 14:28:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:10,634 INFO: 1******** - - [11/Jun/2025 14:28:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:13,900 INFO: 1******** - - [11/Jun/2025 14:28:13] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 14:28:13,932 INFO: 1******** - - [11/Jun/2025 14:28:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:13,932 INFO: 1******** - - [11/Jun/2025 14:28:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:15,488 INFO: 1******** - - [11/Jun/2025 14:28:15] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 14:28:15,519 INFO: 1******** - - [11/Jun/2025 14:28:15] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:15,519 INFO: 1******** - - [11/Jun/2025 14:28:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:29,624 INFO: 1******** - - [11/Jun/2025 14:28:29] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:28:29,687 INFO: 1******** - - [11/Jun/2025 14:28:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:29,687 INFO: 1******** - - [11/Jun/2025 14:28:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:30,505 INFO: 1******** - - [11/Jun/2025 14:28:30] "GET /admin/users/edit/2 HTTP/1.1" 200 -
2025-06-11 14:28:30,538 INFO: 1******** - - [11/Jun/2025 14:28:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:30,538 INFO: 1******** - - [11/Jun/2025 14:28:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:35,195 INFO: 1******** - - [11/Jun/2025 14:28:35] "[32mPOST /admin/users/edit/2 HTTP/1.1[0m" 302 -
2025-06-11 14:28:35,344 INFO: 1******** - - [11/Jun/2025 14:28:35] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:28:35,375 INFO: 1******** - - [11/Jun/2025 14:28:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:35,376 INFO: 1******** - - [11/Jun/2025 14:28:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:38,240 INFO: 1******** - - [11/Jun/2025 14:28:38] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 14:28:38,278 INFO: 1******** - - [11/Jun/2025 14:28:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:38,278 INFO: 1******** - - [11/Jun/2025 14:28:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:43,981 INFO: 1******** - - [11/Jun/2025 14:28:43] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-11 14:28:44,008 INFO: 1******** - - [11/Jun/2025 14:28:44] "GET / HTTP/1.1" 200 -
2025-06-11 14:28:44,044 INFO: 1******** - - [11/Jun/2025 14:28:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:44,047 INFO: 1******** - - [11/Jun/2025 14:28:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:45,514 INFO: 1******** - - [11/Jun/2025 14:28:45] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 14:28:45,551 INFO: 1******** - - [11/Jun/2025 14:28:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:45,551 INFO: 1******** - - [11/Jun/2025 14:28:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:28:49,229 INFO: 1******** - - [11/Jun/2025 14:28:49] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 14:28:49,250 INFO: 1******** - - [11/Jun/2025 14:28:49] "[32mGET /publisher/dashboard HTTP/1.1[0m" 302 -
2025-06-11 14:28:49,261 INFO: 1******** - - [11/Jun/2025 14:28:49] "GET / HTTP/1.1" 200 -
2025-06-11 14:28:49,295 INFO: 1******** - - [11/Jun/2025 14:28:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:28:49,295 INFO: 1******** - - [11/Jun/2025 14:28:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:30:28,457 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 14:30:28,499 INFO:  * Restarting with stat
2025-06-11 14:30:28,901 WARNING:  * Debugger is active!
2025-06-11 14:30:28,909 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:30:37,683 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 14:30:37,735 INFO:  * Restarting with stat
2025-06-11 14:30:38,283 WARNING:  * Debugger is active!
2025-06-11 14:30:38,291 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:30:57,530 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 14:30:57,569 INFO:  * Restarting with stat
2025-06-11 14:30:57,961 WARNING:  * Debugger is active!
2025-06-11 14:30:57,967 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:31:27,800 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 14:31:27,845 INFO:  * Restarting with stat
2025-06-11 14:31:28,265 WARNING:  * Debugger is active!
2025-06-11 14:31:28,272 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:32:00,188 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 14:32:00,227 INFO:  * Restarting with stat
2025-06-11 14:32:00,734 WARNING:  * Debugger is active!
2025-06-11 14:32:00,741 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:32:52,905 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\test_publisher_functionality.py', reloading
2025-06-11 14:32:52,951 INFO:  * Restarting with stat
2025-06-11 14:32:53,338 WARNING:  * Debugger is active!
2025-06-11 14:32:53,345 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:33:24,339 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\test_publisher_functionality.py', reloading
2025-06-11 14:33:24,379 INFO:  * Restarting with stat
2025-06-11 14:33:24,768 WARNING:  * Debugger is active!
2025-06-11 14:33:24,774 INFO:  * Debugger PIN: 122-053-548
2025-06-11 14:36:34,627 INFO: 1******** - - [11/Jun/2025 14:36:34] "GET /publisher/dashboard HTTP/1.1" 200 -
2025-06-11 14:36:34,746 INFO: 1******** - - [11/Jun/2025 14:36:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:34,746 INFO: 1******** - - [11/Jun/2025 14:36:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:37,921 INFO: 1******** - - [11/Jun/2025 14:36:37] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 14:36:37,980 INFO: 1******** - - [11/Jun/2025 14:36:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:37,980 INFO: 1******** - - [11/Jun/2025 14:36:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:39,480 INFO: 1******** - - [11/Jun/2025 14:36:39] "GET /publisher/dashboard HTTP/1.1" 200 -
2025-06-11 14:36:39,514 INFO: 1******** - - [11/Jun/2025 14:36:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:39,515 INFO: 1******** - - [11/Jun/2025 14:36:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:40,449 INFO: 1******** - - [11/Jun/2025 14:36:40] "GET /app/10 HTTP/1.1" 200 -
2025-06-11 14:36:40,478 INFO: 1******** - - [11/Jun/2025 14:36:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:40,478 INFO: 1******** - - [11/Jun/2025 14:36:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:43,616 INFO: 1******** - - [11/Jun/2025 14:36:43] "GET /publisher/apps/edit/10 HTTP/1.1" 200 -
2025-06-11 14:36:43,677 INFO: 1******** - - [11/Jun/2025 14:36:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:43,677 INFO: 1******** - - [11/Jun/2025 14:36:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:46,355 INFO: 1******** - - [11/Jun/2025 14:36:46] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 14:36:46,391 INFO: 1******** - - [11/Jun/2025 14:36:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:46,393 INFO: 1******** - - [11/Jun/2025 14:36:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:47,496 INFO: 1******** - - [11/Jun/2025 14:36:47] "GET /publisher/dashboard HTTP/1.1" 200 -
2025-06-11 14:36:47,530 INFO: 1******** - - [11/Jun/2025 14:36:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:47,532 INFO: 1******** - - [11/Jun/2025 14:36:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:49,393 INFO: 1******** - - [11/Jun/2025 14:36:49] "GET / HTTP/1.1" 200 -
2025-06-11 14:36:49,424 INFO: 1******** - - [11/Jun/2025 14:36:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:49,425 INFO: 1******** - - [11/Jun/2025 14:36:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:51,189 INFO: 1******** - - [11/Jun/2025 14:36:51] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-11 14:36:51,204 INFO: 1******** - - [11/Jun/2025 14:36:51] "GET / HTTP/1.1" 200 -
2025-06-11 14:36:51,287 INFO: 1******** - - [11/Jun/2025 14:36:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:51,287 INFO: 1******** - - [11/Jun/2025 14:36:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:51,943 INFO: 1******** - - [11/Jun/2025 14:36:51] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 14:36:51,972 INFO: 1******** - - [11/Jun/2025 14:36:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:51,973 INFO: 1******** - - [11/Jun/2025 14:36:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:36:57,763 INFO: 1******** - - [11/Jun/2025 14:36:57] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 14:36:57,785 INFO: 1******** - - [11/Jun/2025 14:36:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:36:57,785 INFO: 1******** - - [11/Jun/2025 14:36:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:37:04,847 INFO: 1******** - - [11/Jun/2025 14:37:04] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 14:37:04,873 INFO: 1******** - - [11/Jun/2025 14:37:04] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 14:37:04,899 INFO: 1******** - - [11/Jun/2025 14:37:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:37:04,899 INFO: 1******** - - [11/Jun/2025 14:37:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:37:08,003 INFO: 1******** - - [11/Jun/2025 14:37:08] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 14:37:08,036 INFO: 1******** - - [11/Jun/2025 14:37:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:37:08,036 INFO: 1******** - - [11/Jun/2025 14:37:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:37:09,438 INFO: 1******** - - [11/Jun/2025 14:37:09] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 14:37:09,481 INFO: 1******** - - [11/Jun/2025 14:37:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:37:09,481 INFO: 1******** - - [11/Jun/2025 14:37:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:37:09,816 INFO: 1******** - - [11/Jun/2025 14:37:09] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 14:37:09,846 INFO: 1******** - - [11/Jun/2025 14:37:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:37:09,847 INFO: 1******** - - [11/Jun/2025 14:37:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:37:10,652 INFO: 1******** - - [11/Jun/2025 14:37:10] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 14:37:10,679 INFO: 1******** - - [11/Jun/2025 14:37:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:37:10,679 INFO: 1******** - - [11/Jun/2025 14:37:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:37:11,412 INFO: 1******** - - [11/Jun/2025 14:37:11] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 14:37:11,440 INFO: 1******** - - [11/Jun/2025 14:37:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 14:37:11,441 INFO: 1******** - - [11/Jun/2025 14:37:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:37:14,421 INFO: 1******** - - [11/Jun/2025 14:37:14] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 14:37:14,465 INFO: 1******** - - [11/Jun/2025 14:37:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 14:37:14,465 INFO: 1******** - - [11/Jun/2025 14:37:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:02:21,808 INFO: 1******** - - [11/Jun/2025 16:02:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:03:12,549 INFO: 1******** - - [11/Jun/2025 16:03:12] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 16:03:12,615 INFO: 1******** - - [11/Jun/2025 16:03:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:03:12,615 INFO: 1******** - - [11/Jun/2025 16:03:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:03:16,558 INFO: 1******** - - [11/Jun/2025 16:03:16] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 16:03:16,591 INFO: 1******** - - [11/Jun/2025 16:03:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:03:16,592 INFO: 1******** - - [11/Jun/2025 16:03:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:03:18,109 INFO: 1******** - - [11/Jun/2025 16:03:18] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:03:23,451 INFO: 1******** - - [11/Jun/2025 16:03:23] "GET /admin/reports?type=suggestions&status=pending HTTP/1.1" 200 -
2025-06-11 16:03:23,499 INFO: 1******** - - [11/Jun/2025 16:03:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:03:23,500 INFO: 1******** - - [11/Jun/2025 16:03:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:03:24,439 INFO: 1******** - - [11/Jun/2025 16:03:24] "GET /admin/reports?type=suggestions&status=resolved HTTP/1.1" 200 -
2025-06-11 16:03:24,467 INFO: 1******** - - [11/Jun/2025 16:03:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:03:24,468 INFO: 1******** - - [11/Jun/2025 16:03:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:03:27,133 INFO: 1******** - - [11/Jun/2025 16:03:27] "GET /admin/reports?type=suggestions&status=resolved HTTP/1.1" 200 -
2025-06-11 16:03:27,163 INFO: 1******** - - [11/Jun/2025 16:03:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:03:27,163 INFO: 1******** - - [11/Jun/2025 16:03:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:03:32,054 INFO: 1******** - - [11/Jun/2025 16:03:32] "[33mPOST /admin/reports/update_status/2 HTTP/1.1[0m" 404 -
2025-06-11 16:03:41,167 INFO: 1******** - - [11/Jun/2025 16:03:41] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 16:03:41,204 INFO: 1******** - - [11/Jun/2025 16:03:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:03:41,204 INFO: 1******** - - [11/Jun/2025 16:03:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:03:44,222 INFO: 1******** - - [11/Jun/2025 16:03:44] "[33mPOST /admin/reports/update_status/1 HTTP/1.1[0m" 404 -
2025-06-11 16:05:57,577 INFO: 1******** - - [11/Jun/2025 16:05:57] "GET /admin/reports?type=abuse&status=reviewed HTTP/1.1" 200 -
2025-06-11 16:05:57,610 INFO: 1******** - - [11/Jun/2025 16:05:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:05:57,610 INFO: 1******** - - [11/Jun/2025 16:05:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:05:58,927 INFO: 1******** - - [11/Jun/2025 16:05:58] "GET /admin/reports?type=abuse&status=resolved HTTP/1.1" 200 -
2025-06-11 16:05:58,958 INFO: 1******** - - [11/Jun/2025 16:05:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:05:58,959 INFO: 1******** - - [11/Jun/2025 16:05:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:00,207 INFO: 1******** - - [11/Jun/2025 16:06:00] "GET /admin/reports?type=abuse&status=pending HTTP/1.1" 200 -
2025-06-11 16:06:00,273 INFO: 1******** - - [11/Jun/2025 16:06:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:00,273 INFO: 1******** - - [11/Jun/2025 16:06:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:02,980 INFO: 1******** - - [11/Jun/2025 16:06:02] "GET / HTTP/1.1" 200 -
2025-06-11 16:06:03,054 INFO: 1******** - - [11/Jun/2025 16:06:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:03,054 INFO: 1******** - - [11/Jun/2025 16:06:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:06,501 INFO: 1******** - - [11/Jun/2025 16:06:06] "GET /?category=RAT HTTP/1.1" 200 -
2025-06-11 16:06:06,564 INFO: 1******** - - [11/Jun/2025 16:06:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:06,577 INFO: 1******** - - [11/Jun/2025 16:06:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:11,997 INFO: 1******** - - [11/Jun/2025 16:06:11] "GET /app/12 HTTP/1.1" 200 -
2025-06-11 16:06:12,106 INFO: 1******** - - [11/Jun/2025 16:06:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:12,106 INFO: 1******** - - [11/Jun/2025 16:06:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:15,483 INFO: 1******** - - [11/Jun/2025 16:06:15] "GET /?search= HTTP/1.1" 200 -
2025-06-11 16:06:15,721 INFO: 1******** - - [11/Jun/2025 16:06:15] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:15,746 INFO: 1******** - - [11/Jun/2025 16:06:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:19,230 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 16:06:19,349 INFO:  * Restarting with stat
2025-06-11 16:06:20,316 WARNING:  * Debugger is active!
2025-06-11 16:06:20,327 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:06:20,571 INFO: 1******** - - [11/Jun/2025 16:06:20] "GET /app/3 HTTP/1.1" 200 -
2025-06-11 16:06:20,756 INFO: 1******** - - [11/Jun/2025 16:06:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:20,758 INFO: 1******** - - [11/Jun/2025 16:06:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:25,425 INFO: 1******** - - [11/Jun/2025 16:06:25] "GET /?search=&category=RAT HTTP/1.1" 200 -
2025-06-11 16:06:25,870 INFO: 1******** - - [11/Jun/2025 16:06:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:25,870 INFO: 1******** - - [11/Jun/2025 16:06:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:30,197 INFO: 1******** - - [11/Jun/2025 16:06:30] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 16:06:30,390 INFO: 1******** - - [11/Jun/2025 16:06:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:06:30,436 INFO: 1******** - - [11/Jun/2025 16:06:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:06:46,295 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 16:06:46,348 INFO:  * Restarting with stat
2025-06-11 16:06:46,754 WARNING:  * Debugger is active!
2025-06-11 16:06:46,760 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:08:53,889 INFO: 1******** - - [11/Jun/2025 16:08:53] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:08:54,032 INFO: 1******** - - [11/Jun/2025 16:08:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:08:54,032 INFO: 1******** - - [11/Jun/2025 16:08:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:08:56,894 INFO: 1******** - - [11/Jun/2025 16:08:56] "[32mGET /admin/reports HTTP/1.1[0m" 302 -
2025-06-11 16:08:56,909 INFO: 1******** - - [11/Jun/2025 16:08:56] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:08:56,934 INFO: 1******** - - [11/Jun/2025 16:08:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:08:56,939 INFO: 1******** - - [11/Jun/2025 16:08:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:09:11,214 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 16:09:11,256 INFO:  * Restarting with stat
2025-06-11 16:09:11,680 WARNING:  * Debugger is active!
2025-06-11 16:09:11,687 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:09:29,920 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 16:09:29,960 INFO:  * Restarting with stat
2025-06-11 16:09:30,386 WARNING:  * Debugger is active!
2025-06-11 16:09:30,395 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:09:43,377 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\publisher.py', reloading
2025-06-11 16:09:43,426 INFO:  * Restarting with stat
2025-06-11 16:09:43,849 WARNING:  * Debugger is active!
2025-06-11 16:09:43,856 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:14:05,864 INFO: 1******** - - [11/Jun/2025 16:14:05] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:14:06,176 INFO: 1******** - - [11/Jun/2025 16:14:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:06,208 INFO: 1******** - - [11/Jun/2025 16:14:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:06,262 INFO: 1******** - - [11/Jun/2025 16:14:06] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:14:07,015 INFO: 1******** - - [11/Jun/2025 16:14:07] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:14:07,084 INFO: 1******** - - [11/Jun/2025 16:14:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:07,085 INFO: 1******** - - [11/Jun/2025 16:14:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:08,746 INFO: 1******** - - [11/Jun/2025 16:14:08] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 16:14:08,810 INFO: 1******** - - [11/Jun/2025 16:14:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:08,810 INFO: 1******** - - [11/Jun/2025 16:14:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:15,215 INFO: 1******** - - [11/Jun/2025 16:14:15] "POST /admin/reports/update_status/1 HTTP/1.1" 200 -
2025-06-11 16:14:16,483 INFO: 1******** - - [11/Jun/2025 16:14:16] "GET /admin/reports HTTP/1.1" 200 -
2025-06-11 16:14:16,516 INFO: 1******** - - [11/Jun/2025 16:14:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:16,516 INFO: 1******** - - [11/Jun/2025 16:14:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:16,568 INFO: 1******** - - [11/Jun/2025 16:14:16] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:14:18,052 INFO: 1******** - - [11/Jun/2025 16:14:18] "GET /admin/reports?type=abuse&status=pending HTTP/1.1" 200 -
2025-06-11 16:14:18,089 INFO: 1******** - - [11/Jun/2025 16:14:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:18,090 INFO: 1******** - - [11/Jun/2025 16:14:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:19,279 INFO: 1******** - - [11/Jun/2025 16:14:19] "GET /admin/reports?type=abuse&status=resolved HTTP/1.1" 200 -
2025-06-11 16:14:19,309 INFO: 1******** - - [11/Jun/2025 16:14:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:19,309 INFO: 1******** - - [11/Jun/2025 16:14:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:20,166 INFO: 1******** - - [11/Jun/2025 16:14:20] "GET /admin/reports?type=abuse&status=reviewed HTTP/1.1" 200 -
2025-06-11 16:14:20,201 INFO: 1******** - - [11/Jun/2025 16:14:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:20,201 INFO: 1******** - - [11/Jun/2025 16:14:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:21,030 INFO: 1******** - - [11/Jun/2025 16:14:21] "GET /admin/reports?type=abuse&status= HTTP/1.1" 200 -
2025-06-11 16:14:21,059 INFO: 1******** - - [11/Jun/2025 16:14:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:21,059 INFO: 1******** - - [11/Jun/2025 16:14:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:21,751 INFO: 1******** - - [11/Jun/2025 16:14:21] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 16:14:21,783 INFO: 1******** - - [11/Jun/2025 16:14:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:21,784 INFO: 1******** - - [11/Jun/2025 16:14:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:24,114 INFO: 1******** - - [11/Jun/2025 16:14:24] "POST /admin/reports/update_status/2 HTTP/1.1" 200 -
2025-06-11 16:14:24,994 INFO: 1******** - - [11/Jun/2025 16:14:24] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 16:14:25,086 INFO: 1******** - - [11/Jun/2025 16:14:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:25,086 INFO: 1******** - - [11/Jun/2025 16:14:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:25,149 INFO: 1******** - - [11/Jun/2025 16:14:25] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:14:26,937 INFO: 1******** - - [11/Jun/2025 16:14:26] "GET /admin/reports?type=suggestions&status=reviewed HTTP/1.1" 200 -
2025-06-11 16:14:26,971 INFO: 1******** - - [11/Jun/2025 16:14:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:26,971 INFO: 1******** - - [11/Jun/2025 16:14:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:28,278 INFO: 1******** - - [11/Jun/2025 16:14:28] "GET /admin/reports?type=suggestions&status=resolved HTTP/1.1" 200 -
2025-06-11 16:14:28,308 INFO: 1******** - - [11/Jun/2025 16:14:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:28,309 INFO: 1******** - - [11/Jun/2025 16:14:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:29,982 INFO: 1******** - - [11/Jun/2025 16:14:29] "GET /admin/reports?type=suggestions&status= HTTP/1.1" 200 -
2025-06-11 16:14:30,011 INFO: 1******** - - [11/Jun/2025 16:14:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:30,012 INFO: 1******** - - [11/Jun/2025 16:14:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:38,656 INFO: 1******** - - [11/Jun/2025 16:14:38] "POST /admin/reports/update_status/2 HTTP/1.1" 200 -
2025-06-11 16:14:39,700 INFO: 1******** - - [11/Jun/2025 16:14:39] "GET /admin/reports?type=suggestions&status= HTTP/1.1" 200 -
2025-06-11 16:14:39,744 INFO: 1******** - - [11/Jun/2025 16:14:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:39,744 INFO: 1******** - - [11/Jun/2025 16:14:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:39,821 INFO: 1******** - - [11/Jun/2025 16:14:39] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:14:42,609 INFO: 1******** - - [11/Jun/2025 16:14:42] "POST /admin/reports/update_status/1 HTTP/1.1" 200 -
2025-06-11 16:14:43,701 INFO: 1******** - - [11/Jun/2025 16:14:43] "GET /admin/reports?type=suggestions&status= HTTP/1.1" 200 -
2025-06-11 16:14:43,760 INFO: 1******** - - [11/Jun/2025 16:14:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:43,760 INFO: 1******** - - [11/Jun/2025 16:14:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:43,819 INFO: 1******** - - [11/Jun/2025 16:14:43] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:14:45,956 INFO: 1******** - - [11/Jun/2025 16:14:45] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 16:14:45,993 INFO: 1******** - - [11/Jun/2025 16:14:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:45,993 INFO: 1******** - - [11/Jun/2025 16:14:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:46,581 INFO: 1******** - - [11/Jun/2025 16:14:46] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 16:14:46,633 INFO: 1******** - - [11/Jun/2025 16:14:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:46,633 INFO: 1******** - - [11/Jun/2025 16:14:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:47,606 INFO: 1******** - - [11/Jun/2025 16:14:47] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 16:14:47,637 INFO: 1******** - - [11/Jun/2025 16:14:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:47,637 INFO: 1******** - - [11/Jun/2025 16:14:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:48,557 INFO: 1******** - - [11/Jun/2025 16:14:48] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 16:14:48,588 INFO: 1******** - - [11/Jun/2025 16:14:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:48,588 INFO: 1******** - - [11/Jun/2025 16:14:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:50,028 INFO: 1******** - - [11/Jun/2025 16:14:50] "GET /admin/reports?type=abuse HTTP/1.1" 200 -
2025-06-11 16:14:50,068 INFO: 1******** - - [11/Jun/2025 16:14:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:50,070 INFO: 1******** - - [11/Jun/2025 16:14:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:50,829 INFO: 1******** - - [11/Jun/2025 16:14:50] "GET /admin/reports?type=suggestions HTTP/1.1" 200 -
2025-06-11 16:14:50,859 INFO: 1******** - - [11/Jun/2025 16:14:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:50,859 INFO: 1******** - - [11/Jun/2025 16:14:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:14:57,856 INFO: 1******** - - [11/Jun/2025 16:14:57] "GET / HTTP/1.1" 200 -
2025-06-11 16:14:57,894 INFO: 1******** - - [11/Jun/2025 16:14:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:14:57,895 INFO: 1******** - - [11/Jun/2025 16:14:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:01,664 INFO: 1******** - - [11/Jun/2025 16:15:01] "GET /app/4 HTTP/1.1" 200 -
2025-06-11 16:15:01,699 INFO: 1******** - - [11/Jun/2025 16:15:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:01,700 INFO: 1******** - - [11/Jun/2025 16:15:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:09,709 INFO: 1******** - - [11/Jun/2025 16:15:09] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 16:15:09,745 INFO: 1******** - - [11/Jun/2025 16:15:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:09,745 INFO: 1******** - - [11/Jun/2025 16:15:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:13,699 INFO: 1******** - - [11/Jun/2025 16:15:13] "GET /admin/users/add HTTP/1.1" 200 -
2025-06-11 16:15:13,731 INFO: 1******** - - [11/Jun/2025 16:15:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:13,732 INFO: 1******** - - [11/Jun/2025 16:15:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:15,003 INFO: 1******** - - [11/Jun/2025 16:15:15] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-11 16:15:15,017 INFO: 1******** - - [11/Jun/2025 16:15:15] "GET / HTTP/1.1" 200 -
2025-06-11 16:15:15,050 INFO: 1******** - - [11/Jun/2025 16:15:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:15,050 INFO: 1******** - - [11/Jun/2025 16:15:15] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:16,160 INFO: 1******** - - [11/Jun/2025 16:15:16] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:15:16,198 INFO: 1******** - - [11/Jun/2025 16:15:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:16,199 INFO: 1******** - - [11/Jun/2025 16:15:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:19,474 INFO: 1******** - - [11/Jun/2025 16:15:19] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 16:15:19,497 INFO: 1******** - - [11/Jun/2025 16:15:19] "GET /publisher/dashboard HTTP/1.1" 200 -
2025-06-11 16:15:19,533 INFO: 1******** - - [11/Jun/2025 16:15:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:19,534 INFO: 1******** - - [11/Jun/2025 16:15:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:25,544 INFO: 1******** - - [11/Jun/2025 16:15:25] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 16:15:25,578 INFO: 1******** - - [11/Jun/2025 16:15:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:25,580 INFO: 1******** - - [11/Jun/2025 16:15:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:28,330 INFO: 1******** - - [11/Jun/2025 16:15:28] "GET /publisher/apps/add HTTP/1.1" 200 -
2025-06-11 16:15:28,382 INFO: 1******** - - [11/Jun/2025 16:15:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:28,382 INFO: 1******** - - [11/Jun/2025 16:15:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:31,044 INFO: 1******** - - [11/Jun/2025 16:15:31] "POST /publisher/apps/delete/10 HTTP/1.1" 200 -
2025-06-11 16:15:31,891 INFO: 1******** - - [11/Jun/2025 16:15:31] "GET /publisher/dashboard HTTP/1.1" 200 -
2025-06-11 16:15:31,924 INFO: 1******** - - [11/Jun/2025 16:15:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:31,924 INFO: 1******** - - [11/Jun/2025 16:15:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:15:32,272 INFO: 1******** - - [11/Jun/2025 16:15:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:15:33,416 INFO: 1******** - - [11/Jun/2025 16:15:33] "GET /publisher/apps/edit/16 HTTP/1.1" 200 -
2025-06-11 16:15:33,448 INFO: 1******** - - [11/Jun/2025 16:15:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:15:33,456 INFO: 1******** - - [11/Jun/2025 16:15:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:16:55,788 INFO: 1******** - - [11/Jun/2025 16:16:55] "GET /publisher/shortlinks HTTP/1.1" 200 -
2025-06-11 16:16:55,824 INFO: 1******** - - [11/Jun/2025 16:16:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:16:55,826 INFO: 1******** - - [11/Jun/2025 16:16:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:16:57,197 INFO: 1******** - - [11/Jun/2025 16:16:57] "GET /publisher/dashboard HTTP/1.1" 200 -
2025-06-11 16:16:57,233 INFO: 1******** - - [11/Jun/2025 16:16:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:16:57,234 INFO: 1******** - - [11/Jun/2025 16:16:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:18:32,152 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 16:18:32,219 INFO:  * Restarting with stat
2025-06-11 16:18:32,656 WARNING:  * Debugger is active!
2025-06-11 16:18:32,664 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:19:29,154 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 16:19:29,214 INFO:  * Restarting with stat
2025-06-11 16:19:29,763 WARNING:  * Debugger is active!
2025-06-11 16:19:29,773 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:19:55,927 INFO: 1******** - - [11/Jun/2025 16:19:55] "GET /publisher/dashboard HTTP/1.1" 200 -
2025-06-11 16:19:56,048 INFO: 1******** - - [11/Jun/2025 16:19:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:19:56,049 INFO: 1******** - - [11/Jun/2025 16:19:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:19:56,577 INFO: 1******** - - [11/Jun/2025 16:19:56] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:19:59,859 INFO: 1******** - - [11/Jun/2025 16:19:59] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-11 16:19:59,892 INFO: 1******** - - [11/Jun/2025 16:19:59] "GET / HTTP/1.1" 200 -
2025-06-11 16:19:59,949 INFO: 1******** - - [11/Jun/2025 16:19:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:19:59,966 INFO: 1******** - - [11/Jun/2025 16:19:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:20:01,651 INFO: 1******** - - [11/Jun/2025 16:20:01] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:20:01,684 INFO: 1******** - - [11/Jun/2025 16:20:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:20:01,684 INFO: 1******** - - [11/Jun/2025 16:20:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:20:04,114 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 16:20:04,217 INFO:  * Restarting with stat
2025-06-11 16:20:04,758 WARNING:  * Debugger is active!
2025-06-11 16:20:04,767 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:20:08,308 INFO: 1******** - - [11/Jun/2025 16:20:08] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 16:20:08,350 INFO: 1******** - - [11/Jun/2025 16:20:08] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:20:08,508 INFO: 1******** - - [11/Jun/2025 16:20:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:20:08,508 INFO: 1******** - - [11/Jun/2025 16:20:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:20:15,540 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 16:20:15,583 INFO:  * Restarting with stat
2025-06-11 16:20:16,282 WARNING:  * Debugger is active!
2025-06-11 16:20:16,289 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:20:52,537 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\common.py', reloading
2025-06-11 16:20:52,584 INFO:  * Restarting with stat
2025-06-11 16:20:53,626 WARNING:  * Debugger is active!
2025-06-11 16:20:53,639 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:21:12,073 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\common.py', reloading
2025-06-11 16:21:12,123 INFO:  * Restarting with stat
2025-06-11 16:21:12,974 WARNING:  * Debugger is active!
2025-06-11 16:21:12,983 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:21:34,562 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 16:21:34,672 INFO:  * Restarting with stat
2025-06-11 16:21:35,868 WARNING:  * Debugger is active!
2025-06-11 16:21:35,878 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:22:03,928 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 16:22:03,993 INFO:  * Restarting with stat
2025-06-11 16:22:04,846 WARNING:  * Debugger is active!
2025-06-11 16:22:04,854 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:23:09,281 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 16:23:09,331 INFO:  * Restarting with stat
2025-06-11 16:23:10,399 WARNING:  * Debugger is active!
2025-06-11 16:23:10,407 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:23:30,924 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 16:23:31,012 INFO:  * Restarting with stat
2025-06-11 16:23:32,051 WARNING:  * Debugger is active!
2025-06-11 16:23:32,080 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:27:12,105 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 16:27:12,205 INFO:  * Restarting with stat
2025-06-11 16:27:13,189 WARNING:  * Debugger is active!
2025-06-11 16:27:13,198 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:27:32,364 INFO: 1******** - - [11/Jun/2025 16:27:32] "GET /admin/apps/add HTTP/1.1" 200 -
2025-06-11 16:27:32,505 INFO: 1******** - - [11/Jun/2025 16:27:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:27:32,506 INFO: 1******** - - [11/Jun/2025 16:27:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:27:37,074 INFO: 1******** - - [11/Jun/2025 16:27:37] "[32mGET /admin/blocked-ips HTTP/1.1[0m" 302 -
2025-06-11 16:27:37,104 INFO: 1******** - - [11/Jun/2025 16:27:37] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:27:37,166 INFO: 1******** - - [11/Jun/2025 16:27:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:27:37,166 INFO: 1******** - - [11/Jun/2025 16:27:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:27:44,645 INFO: 1******** - - [11/Jun/2025 16:27:44] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-11 16:27:44,683 INFO: 1******** - - [11/Jun/2025 16:27:44] "GET / HTTP/1.1" 200 -
2025-06-11 16:27:44,841 INFO: 1******** - - [11/Jun/2025 16:27:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:27:44,841 INFO: 1******** - - [11/Jun/2025 16:27:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:27:45,926 INFO: 1******** - - [11/Jun/2025 16:27:45] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:27:45,960 INFO: 1******** - - [11/Jun/2025 16:27:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:27:45,961 INFO: 1******** - - [11/Jun/2025 16:27:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:28:30,104 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 16:28:30,152 INFO:  * Restarting with stat
2025-06-11 16:28:31,065 WARNING:  * Debugger is active!
2025-06-11 16:28:31,072 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:29:25,850 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 16:29:25,921 INFO:  * Restarting with stat
2025-06-11 16:29:27,125 WARNING:  * Debugger is active!
2025-06-11 16:29:27,132 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:29:32,667 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 16:29:32,733 INFO:  * Restarting with stat
2025-06-11 16:29:33,969 WARNING:  * Debugger is active!
2025-06-11 16:29:33,978 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:29:35,332 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 16:29:35,480 INFO:  * Restarting with stat
2025-06-11 16:29:36,938 WARNING:  * Debugger is active!
2025-06-11 16:29:36,945 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:29:41,814 INFO: 1******** - - [11/Jun/2025 16:29:41] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:29:41,951 INFO: 1******** - - [11/Jun/2025 16:29:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:29:41,952 INFO: 1******** - - [11/Jun/2025 16:29:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:29:42,281 INFO: 1******** - - [11/Jun/2025 16:29:42] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:31:50,891 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 16:31:50,988 INFO:  * Restarting with stat
2025-06-11 16:31:52,004 WARNING:  * Debugger is active!
2025-06-11 16:31:52,011 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:32:00,733 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 16:32:00,835 INFO:  * Restarting with stat
2025-06-11 16:32:02,136 WARNING:  * Debugger is active!
2025-06-11 16:32:02,146 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:32:06,294 INFO: 1******** - - [11/Jun/2025 16:32:06] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:32:06,410 INFO: 1******** - - [11/Jun/2025 16:32:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:32:06,410 INFO: 1******** - - [11/Jun/2025 16:32:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:32:06,938 INFO: 1******** - - [11/Jun/2025 16:32:06] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:32:13,251 INFO: 1******** - - [11/Jun/2025 16:32:13] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:32:13,297 INFO: 1******** - - [11/Jun/2025 16:32:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:32:13,298 INFO: 1******** - - [11/Jun/2025 16:32:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:34:17,547 INFO: 1******** - - [11/Jun/2025 16:34:17] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:34:17,587 INFO: 1******** - - [11/Jun/2025 16:34:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:34:17,587 INFO: 1******** - - [11/Jun/2025 16:34:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:34:18,260 INFO: 1******** - - [11/Jun/2025 16:34:18] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:36:45,150 INFO: 1******** - - [11/Jun/2025 16:36:45] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:36:45,188 INFO: 1******** - - [11/Jun/2025 16:36:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:36:45,197 INFO: 1******** - - [11/Jun/2025 16:36:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:36:45,813 INFO: 1******** - - [11/Jun/2025 16:36:45] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:37:15,777 INFO: 1******** - - [11/Jun/2025 16:37:15] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:37:15,830 INFO: 1******** - - [11/Jun/2025 16:37:15] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:37:15,834 INFO: 1******** - - [11/Jun/2025 16:37:15] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 16:37:16,359 INFO: 1******** - - [11/Jun/2025 16:37:16] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:37:25,128 INFO: 1******** - - [11/Jun/2025 16:37:25] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:37:25,172 INFO: 1******** - - [11/Jun/2025 16:37:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:37:25,181 INFO: 1******** - - [11/Jun/2025 16:37:25] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 16:37:27,434 INFO: 1******** - - [11/Jun/2025 16:37:27] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:37:27,701 INFO: 1******** - - [11/Jun/2025 16:37:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:37:27,701 INFO: 1******** - - [11/Jun/2025 16:37:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:37:28,751 INFO: 1******** - - [11/Jun/2025 16:37:28] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:37:28,801 INFO: 1******** - - [11/Jun/2025 16:37:28] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:37:37,667 INFO: 1******** - - [11/Jun/2025 16:37:37] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:37:37,707 INFO: 1******** - - [11/Jun/2025 16:37:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:37:37,708 INFO: 1******** - - [11/Jun/2025 16:37:37] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 16:37:38,058 INFO: 1******** - - [11/Jun/2025 16:37:38] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:37:54,061 INFO: 1******** - - [11/Jun/2025 16:37:54] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:37:54,100 INFO: 1******** - - [11/Jun/2025 16:37:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:37:54,102 INFO: 1******** - - [11/Jun/2025 16:37:54] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 16:37:54,425 INFO: 1******** - - [11/Jun/2025 16:37:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:37:57,284 INFO: 1******** - - [11/Jun/2025 16:37:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:38:07,337 INFO: 1******** - - [11/Jun/2025 16:38:07] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:38:07,390 INFO: 1******** - - [11/Jun/2025 16:38:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:38:07,390 INFO: 1******** - - [11/Jun/2025 16:38:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:38:12,047 INFO: 1******** - - [11/Jun/2025 16:38:12] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:38:12,092 INFO: 1******** - - [11/Jun/2025 16:38:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:38:12,093 INFO: 1******** - - [11/Jun/2025 16:38:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:38:32,652 INFO: 1******** - - [11/Jun/2025 16:38:32] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 16:38:32,684 INFO: 1******** - - [11/Jun/2025 16:38:32] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:38:32,786 INFO: 1******** - - [11/Jun/2025 16:38:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:38:32,786 INFO: 1******** - - [11/Jun/2025 16:38:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:38:37,091 INFO: 1******** - - [11/Jun/2025 16:38:37] "[32mGET /admin/blocked-ips HTTP/1.1[0m" 302 -
2025-06-11 16:38:37,106 INFO: 1******** - - [11/Jun/2025 16:38:37] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:38:37,220 INFO: 1******** - - [11/Jun/2025 16:38:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:38:37,220 INFO: 1******** - - [11/Jun/2025 16:38:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:40:37,607 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 16:40:37,666 INFO:  * Restarting with stat
2025-06-11 16:40:38,827 WARNING:  * Debugger is active!
2025-06-11 16:40:38,834 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:43:39,654 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\common.py', reloading
2025-06-11 16:43:39,700 INFO:  * Restarting with stat
2025-06-11 16:43:40,533 WARNING:  * Debugger is active!
2025-06-11 16:43:40,540 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:53:17,475 INFO: 1******** - - [11/Jun/2025 16:53:17] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:53:17,859 INFO: 1******** - - [11/Jun/2025 16:53:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:17,860 INFO: 1******** - - [11/Jun/2025 16:53:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:53:18,623 INFO: 1******** - - [11/Jun/2025 16:53:18] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:53:19,306 INFO: 1******** - - [11/Jun/2025 16:53:19] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:53:19,359 INFO: 1******** - - [11/Jun/2025 16:53:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:53:19,359 INFO: 1******** - - [11/Jun/2025 16:53:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:38,663 INFO: 1******** - - [11/Jun/2025 16:53:38] "GET / HTTP/1.1" 200 -
2025-06-11 16:53:38,717 INFO: 1******** - - [11/Jun/2025 16:53:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:53:38,717 INFO: 1******** - - [11/Jun/2025 16:53:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:42,101 INFO: 1******** - - [11/Jun/2025 16:53:42] "GET /app/2 HTTP/1.1" 200 -
2025-06-11 16:53:42,146 INFO: 1******** - - [11/Jun/2025 16:53:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:42,148 INFO: 1******** - - [11/Jun/2025 16:53:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:53:46,418 INFO: 1******** - - [11/Jun/2025 16:53:46] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 16:53:46,450 INFO: 1******** - - [11/Jun/2025 16:53:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:46,451 INFO: 1******** - - [11/Jun/2025 16:53:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:53:48,189 INFO: 1******** - - [11/Jun/2025 16:53:48] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:53:48,225 INFO: 1******** - - [11/Jun/2025 16:53:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:48,225 INFO: 1******** - - [11/Jun/2025 16:53:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:53:53,225 INFO: 1******** - - [11/Jun/2025 16:53:53] "[32mPOST /admin/blocked-ips/unblock/************* HTTP/1.1[0m" 302 -
2025-06-11 16:53:53,234 INFO: 1******** - - [11/Jun/2025 16:53:53] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:53:53,257 INFO: 1******** - - [11/Jun/2025 16:53:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:53:53,258 INFO: 1******** - - [11/Jun/2025 16:53:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:59,666 INFO: 1******** - - [11/Jun/2025 16:53:59] "[32mPOST /admin/blocked-ips/block HTTP/1.1[0m" 302 -
2025-06-11 16:53:59,675 INFO: 1******** - - [11/Jun/2025 16:53:59] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:53:59,701 INFO: 1******** - - [11/Jun/2025 16:53:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:53:59,702 INFO: 1******** - - [11/Jun/2025 16:53:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:54:01,105 INFO: 1******** - - [11/Jun/2025 16:54:01] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:54:01,156 INFO: 1******** - - [11/Jun/2025 16:54:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:54:01,156 INFO: 1******** - - [11/Jun/2025 16:54:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:54:01,207 INFO: 1******** - - [11/Jun/2025 16:54:01] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:54:02,211 INFO: 1******** - - [11/Jun/2025 16:54:02] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:54:02,267 INFO: 1******** - - [11/Jun/2025 16:54:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:54:02,267 INFO: 1******** - - [11/Jun/2025 16:54:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:54:02,311 INFO: 1******** - - [11/Jun/2025 16:54:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:54:07,405 INFO: 1******** - - [11/Jun/2025 16:54:07] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:54:07,467 INFO: 1******** - - [11/Jun/2025 16:54:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:54:07,468 INFO: 1******** - - [11/Jun/2025 16:54:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:54:07,522 INFO: 1******** - - [11/Jun/2025 16:54:07] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:54:08,171 INFO: 1******** - - [11/Jun/2025 16:54:08] "GET /admin/blocked-ips HTTP/1.1" 200 -
2025-06-11 16:54:08,195 INFO: 1******** - - [11/Jun/2025 16:54:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:54:08,198 INFO: 1******** - - [11/Jun/2025 16:54:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:54:08,281 INFO: 1******** - - [11/Jun/2025 16:54:08] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:54:12,329 INFO: 1******** - - [11/Jun/2025 16:54:12] "GET / HTTP/1.1" 200 -
2025-06-11 16:54:12,374 INFO: 1******** - - [11/Jun/2025 16:54:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:54:12,375 INFO: 1******** - - [11/Jun/2025 16:54:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:54:13,567 INFO: 1******** - - [11/Jun/2025 16:54:13] "GET / HTTP/1.1" 200 -
2025-06-11 16:54:13,620 INFO: 1******** - - [11/Jun/2025 16:54:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:54:13,621 INFO: 1******** - - [11/Jun/2025 16:54:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:54:13,674 INFO: 1******** - - [11/Jun/2025 16:54:13] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:55:10,766 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 16:55:10,836 INFO:  * Restarting with stat
2025-06-11 16:55:11,749 WARNING:  * Debugger is active!
2025-06-11 16:55:11,761 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:55:25,897 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 16:55:25,943 INFO:  * Restarting with stat
2025-06-11 16:57:28,472 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 16:57:28,472 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 16:57:28,476 INFO:  * Restarting with stat
2025-06-11 16:57:29,372 WARNING:  * Debugger is active!
2025-06-11 16:57:29,379 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:57:29,490 INFO: 1******** - - [11/Jun/2025 16:57:29] "GET / HTTP/1.1" 200 -
2025-06-11 16:57:29,495 INFO: 1******** - - [11/Jun/2025 16:57:29] "GET / HTTP/1.1" 200 -
2025-06-11 16:57:29,621 INFO: 1******** - - [11/Jun/2025 16:57:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:57:29,622 INFO: 1******** - - [11/Jun/2025 16:57:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:57:29,862 INFO: 1******** - - [11/Jun/2025 16:57:29] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 16:57:33,283 INFO: 1******** - - [11/Jun/2025 16:57:33] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 16:57:33,316 INFO: 1******** - - [11/Jun/2025 16:57:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:57:33,317 INFO: 1******** - - [11/Jun/2025 16:57:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:57:36,339 INFO: 1******** - - [11/Jun/2025 16:57:36] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:57:36,380 INFO: 1******** - - [11/Jun/2025 16:57:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:57:36,380 INFO: 1******** - - [11/Jun/2025 16:57:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:57:52,319 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-06-11 16:57:52,464 INFO:  * Restarting with stat
2025-06-11 16:57:54,000 WARNING:  * Debugger is active!
2025-06-11 16:57:54,009 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:58:02,176 INFO: 1******** - - [11/Jun/2025 16:58:02] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 16:58:02,329 INFO: 1******** - - [11/Jun/2025 16:58:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 16:58:02,330 INFO: 1******** - - [11/Jun/2025 16:58:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 16:59:43,533 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 16:59:43,534 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 16:59:43,539 INFO:  * Restarting with stat
2025-06-11 16:59:44,504 WARNING:  * Debugger is active!
2025-06-11 16:59:44,511 INFO:  * Debugger PIN: 122-053-548
2025-06-11 16:59:45,104 INFO: 1******** - - [11/Jun/2025 16:59:45] "[31m[1mPOST /auth/login HTTP/1.1[0m" 403 -
2025-06-11 17:00:20,010 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:00:20,112 INFO:  * Restarting with stat
2025-06-11 17:00:21,161 WARNING:  * Debugger is active!
2025-06-11 17:00:21,172 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:00:44,942 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\main.py', reloading
2025-06-11 17:00:45,027 INFO:  * Restarting with stat
2025-06-11 17:00:46,382 WARNING:  * Debugger is active!
2025-06-11 17:00:46,392 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:01:24,331 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:01:24,405 INFO:  * Restarting with stat
2025-06-11 17:01:26,617 WARNING:  * Debugger is active!
2025-06-11 17:01:26,629 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:01:48,537 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:01:48,611 INFO:  * Restarting with stat
2025-06-11 17:01:49,883 WARNING:  * Debugger is active!
2025-06-11 17:01:49,898 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:01:52,395 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:01:52,506 INFO:  * Restarting with stat
2025-06-11 17:01:54,154 WARNING:  * Debugger is active!
2025-06-11 17:01:54,170 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:01:59,841 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:01:59,952 INFO:  * Restarting with stat
2025-06-11 17:02:01,071 WARNING:  * Debugger is active!
2025-06-11 17:02:01,078 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:02:09,083 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:02:09,185 INFO:  * Restarting with stat
2025-06-11 17:02:38,726 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 17:02:38,727 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 17:02:38,730 INFO:  * Restarting with stat
2025-06-11 17:02:39,583 WARNING:  * Debugger is active!
2025-06-11 17:02:39,590 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:02:40,188 INFO: 1******** - - [11/Jun/2025 17:02:40] "GET / HTTP/1.1" 200 -
2025-06-11 17:02:40,350 INFO: 1******** - - [11/Jun/2025 17:02:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:02:40,355 INFO: 1******** - - [11/Jun/2025 17:02:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:02:40,538 INFO: 1******** - - [11/Jun/2025 17:02:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,390 INFO: 1******** - - [11/Jun/2025 17:02:42] "GET / HTTP/1.1" 200 -
2025-06-11 17:02:42,419 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,419 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,481 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,598 INFO: 1******** - - [11/Jun/2025 17:02:42] "GET / HTTP/1.1" 200 -
2025-06-11 17:02:42,626 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,626 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,692 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,799 INFO: 1******** - - [11/Jun/2025 17:02:42] "GET / HTTP/1.1" 200 -
2025-06-11 17:02:42,824 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,826 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:02:42,897 INFO: 1******** - - [11/Jun/2025 17:02:42] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:02:48,506 INFO: 1******** - - [11/Jun/2025 17:02:48] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 17:02:48,705 INFO: 1******** - - [11/Jun/2025 17:02:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:02:48,745 INFO: 1******** - - [11/Jun/2025 17:02:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:03:04,206 INFO: 1******** - - [11/Jun/2025 17:03:04] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:03:04,640 INFO: 1******** - - [11/Jun/2025 17:03:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:03:04,640 INFO: 1******** - - [11/Jun/2025 17:03:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:05:32,599 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 17:05:33,051 INFO:  * Restarting with stat
2025-06-11 17:05:36,641 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 17:05:36,642 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 17:05:36,644 INFO:  * Restarting with stat
2025-06-11 17:05:37,545 WARNING:  * Debugger is active!
2025-06-11 17:05:37,553 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:05:37,647 INFO: 1******** - - [11/Jun/2025 17:05:37] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:05:37,801 INFO: 1******** - - [11/Jun/2025 17:05:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:05:37,801 INFO: 1******** - - [11/Jun/2025 17:05:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:05:39,826 INFO: 1******** - - [11/Jun/2025 17:05:39] "GET / HTTP/1.1" 200 -
2025-06-11 17:05:39,858 INFO: 1******** - - [11/Jun/2025 17:05:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:05:39,858 INFO: 1******** - - [11/Jun/2025 17:05:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:05:39,926 INFO: 1******** - - [11/Jun/2025 17:05:39] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:05:49,129 INFO: 1******** - - [11/Jun/2025 17:05:49] "GET / HTTP/1.1" 200 -
2025-06-11 17:05:49,171 INFO: 1******** - - [11/Jun/2025 17:05:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:05:49,174 INFO: 1******** - - [11/Jun/2025 17:05:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:05:49,246 INFO: 1******** - - [11/Jun/2025 17:05:49] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:05:49,992 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:05:50,087 INFO:  * Restarting with stat
2025-06-11 17:05:51,393 WARNING:  * Debugger is active!
2025-06-11 17:05:51,407 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:05:51,513 INFO: 1******** - - [11/Jun/2025 17:05:51] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:05:53,778 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:05:53,855 INFO:  * Restarting with stat
2025-06-11 17:05:55,038 WARNING:  * Debugger is active!
2025-06-11 17:05:55,057 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:06:11,663 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\admin.py', reloading
2025-06-11 17:06:11,878 INFO:  * Restarting with stat
2025-06-11 17:06:12,997 WARNING:  * Debugger is active!
2025-06-11 17:06:13,026 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:07:28,186 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 17:07:28,330 INFO:  * Restarting with stat
2025-06-11 17:07:30,546 WARNING:  * Debugger is active!
2025-06-11 17:07:30,616 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:07:48,398 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:07:48,471 INFO:  * Restarting with stat
2025-06-11 17:07:49,898 WARNING:  * Debugger is active!
2025-06-11 17:07:49,911 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:08:09,712 INFO: 1******** - - [11/Jun/2025 17:08:09] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:08:10,505 INFO: 1******** - - [11/Jun/2025 17:08:10] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:08:10,715 INFO: 1******** - - [11/Jun/2025 17:08:10] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:08:10,903 INFO: 1******** - - [11/Jun/2025 17:08:10] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:08:11,083 INFO: 1******** - - [11/Jun/2025 17:08:11] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:08:11,235 INFO: 1******** - - [11/Jun/2025 17:08:11] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:08:11,423 INFO: 1******** - - [11/Jun/2025 17:08:11] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:08:11,617 INFO: 1******** - - [11/Jun/2025 17:08:11] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:09:37,816 INFO: 1******** - - [11/Jun/2025 17:09:37] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:09:38,433 INFO: 1******** - - [11/Jun/2025 17:09:38] "[31m[1mGET / HTTP/1.1[0m" 403 -
2025-06-11 17:09:46,440 INFO: 1******** - - [11/Jun/2025 17:09:46] "GET / HTTP/1.1" 200 -
2025-06-11 17:09:46,636 INFO: 1******** - - [11/Jun/2025 17:09:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:09:46,637 INFO: 1******** - - [11/Jun/2025 17:09:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:09:47,794 INFO: 1******** - - [11/Jun/2025 17:09:47] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 17:09:47,847 INFO: 1******** - - [11/Jun/2025 17:09:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:09:47,849 INFO: 1******** - - [11/Jun/2025 17:09:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:09:49,340 INFO: 1******** - - [11/Jun/2025 17:09:49] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:09:50,155 INFO: 1******** - - [11/Jun/2025 17:09:50] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:09:50,244 INFO: 1******** - - [11/Jun/2025 17:09:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:09:50,244 INFO: 1******** - - [11/Jun/2025 17:09:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:09:59,463 INFO: 1******** - - [11/Jun/2025 17:09:59] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:09:59,524 INFO: 1******** - - [11/Jun/2025 17:09:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:09:59,525 INFO: 1******** - - [11/Jun/2025 17:09:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:10:25,572 INFO: 1******** - - [11/Jun/2025 17:10:25] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:10:25,664 INFO: 1******** - - [11/Jun/2025 17:10:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:10:25,665 INFO: 1******** - - [11/Jun/2025 17:10:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:10:39,433 INFO: 1******** - - [11/Jun/2025 17:10:39] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:10:39,487 INFO: 1******** - - [11/Jun/2025 17:10:39] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:10:39,488 INFO: 1******** - - [11/Jun/2025 17:10:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:11:41,727 INFO: 1******** - - [11/Jun/2025 17:11:41] "GET / HTTP/1.1" 200 -
2025-06-11 17:11:41,819 INFO: 1******** - - [11/Jun/2025 17:11:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:11:41,819 INFO: 1******** - - [11/Jun/2025 17:11:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:11:44,993 INFO: 1******** - - [11/Jun/2025 17:11:44] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 17:11:45,053 INFO: 1******** - - [11/Jun/2025 17:11:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:11:45,055 INFO: 1******** - - [11/Jun/2025 17:11:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:11:50,256 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:11:50,366 INFO:  * Restarting with stat
2025-06-11 17:11:52,045 WARNING:  * Debugger is active!
2025-06-11 17:11:52,059 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:12:01,643 INFO: 1******** - - [11/Jun/2025 17:12:01] "[32mPOST /auth/login HTTP/1.1[0m" 302 -
2025-06-11 17:12:01,696 INFO: 1******** - - [11/Jun/2025 17:12:01] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 17:12:01,854 INFO: 1******** - - [11/Jun/2025 17:12:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:12:01,856 INFO: 1******** - - [11/Jun/2025 17:12:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:12:08,513 INFO: 1******** - - [11/Jun/2025 17:12:08] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 17:12:08,553 INFO: 1******** - - [11/Jun/2025 17:12:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:12:08,554 INFO: 1******** - - [11/Jun/2025 17:12:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:12:11,729 INFO: 1******** - - [11/Jun/2025 17:12:11] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 17:12:11,796 INFO: 1******** - - [11/Jun/2025 17:12:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:12:11,796 INFO: 1******** - - [11/Jun/2025 17:12:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:12:18,527 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:12:18,608 INFO:  * Restarting with stat
2025-06-11 17:12:19,980 WARNING:  * Debugger is active!
2025-06-11 17:12:19,992 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:12:23,269 INFO: 1******** - - [11/Jun/2025 17:12:23] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 17:12:23,420 INFO: 1******** - - [11/Jun/2025 17:12:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:12:23,421 INFO: 1******** - - [11/Jun/2025 17:12:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:12:47,569 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 17:12:47,849 INFO:  * Restarting with stat
2025-06-11 17:12:51,130 WARNING:  * Debugger is active!
2025-06-11 17:12:51,156 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:14:07,274 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 17:14:07,418 INFO:  * Restarting with stat
2025-06-11 17:14:09,591 WARNING:  * Debugger is active!
2025-06-11 17:14:09,606 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:14:15,688 INFO: 1******** - - [11/Jun/2025 17:14:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:14:37,554 INFO: 1******** - - [11/Jun/2025 17:14:37] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 17:14:37,766 INFO: 1******** - - [11/Jun/2025 17:14:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:14:37,767 INFO: 1******** - - [11/Jun/2025 17:14:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:15:40,163 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 17:15:40,288 INFO:  * Restarting with stat
2025-06-11 17:15:42,133 WARNING:  * Debugger is active!
2025-06-11 17:15:42,146 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:15:42,309 INFO: 1******** - - [11/Jun/2025 17:15:42] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 17:15:42,565 INFO: 1******** - - [11/Jun/2025 17:15:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:15:42,567 INFO: 1******** - - [11/Jun/2025 17:15:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:43,431 INFO: 1******** - - [11/Jun/2025 17:16:43] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 17:16:43,541 INFO: 1******** - - [11/Jun/2025 17:16:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:43,543 INFO: 1******** - - [11/Jun/2025 17:16:43] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 17:16:43,680 INFO: 1******** - - [11/Jun/2025 17:16:43] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:16:44,324 INFO: 1******** - - [11/Jun/2025 17:16:44] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 17:16:44,413 INFO: 1******** - - [11/Jun/2025 17:16:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:16:44,414 INFO: 1******** - - [11/Jun/2025 17:16:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:45,606 INFO: 1******** - - [11/Jun/2025 17:16:45] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 17:16:45,659 INFO: 1******** - - [11/Jun/2025 17:16:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:45,680 INFO: 1******** - - [11/Jun/2025 17:16:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:16:46,387 INFO: 1******** - - [11/Jun/2025 17:16:46] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 17:16:46,437 INFO: 1******** - - [11/Jun/2025 17:16:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:46,440 INFO: 1******** - - [11/Jun/2025 17:16:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:16:46,945 INFO: 1******** - - [11/Jun/2025 17:16:46] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 17:16:47,045 INFO: 1******** - - [11/Jun/2025 17:16:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:16:47,045 INFO: 1******** - - [11/Jun/2025 17:16:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:47,497 INFO: 1******** - - [11/Jun/2025 17:16:47] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 17:16:47,533 INFO: 1******** - - [11/Jun/2025 17:16:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:47,533 INFO: 1******** - - [11/Jun/2025 17:16:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:16:49,507 INFO: 1******** - - [11/Jun/2025 17:16:49] "GET /admin/dashboard HTTP/1.1" 200 -
2025-06-11 17:16:49,678 INFO: 1******** - - [11/Jun/2025 17:16:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:16:49,695 INFO: 1******** - - [11/Jun/2025 17:16:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:17:09,515 INFO: 1******** - - [11/Jun/2025 17:17:09] "[32mGET /auth/logout HTTP/1.1[0m" 302 -
2025-06-11 17:17:09,581 INFO: 1******** - - [11/Jun/2025 17:17:09] "GET / HTTP/1.1" 200 -
2025-06-11 17:17:09,694 INFO: 1******** - - [11/Jun/2025 17:17:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:17:09,694 INFO: 1******** - - [11/Jun/2025 17:17:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:17:10,917 INFO: 1******** - - [11/Jun/2025 17:17:10] "GET /auth/login HTTP/1.1" 200 -
2025-06-11 17:17:11,026 INFO: 1******** - - [11/Jun/2025 17:17:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:17:11,030 INFO: 1******** - - [11/Jun/2025 17:17:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:17:12,872 INFO: 1******** - - [11/Jun/2025 17:17:12] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:17:12,926 INFO: 1******** - - [11/Jun/2025 17:17:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:17:12,927 INFO: 1******** - - [11/Jun/2025 17:17:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:17:37,844 INFO: 1******** - - [11/Jun/2025 17:17:37] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:17:37,901 INFO: 1******** - - [11/Jun/2025 17:17:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:17:37,902 INFO: 1******** - - [11/Jun/2025 17:17:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:17:51,811 INFO: 1******** - - [11/Jun/2025 17:17:51] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:17:51,877 INFO: 1******** - - [11/Jun/2025 17:17:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:17:51,878 INFO: 1******** - - [11/Jun/2025 17:17:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:19:48,242 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\quick_test.py', reloading
2025-06-11 17:19:48,409 INFO:  * Restarting with stat
2025-06-11 17:19:49,716 WARNING:  * Debugger is active!
2025-06-11 17:19:49,741 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:20:35,504 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 17:20:35,593 INFO:  * Restarting with stat
2025-06-11 17:20:37,499 WARNING:  * Debugger is active!
2025-06-11 17:20:37,514 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:22:36,811 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 17:22:36,974 INFO:  * Restarting with stat
2025-06-11 17:22:39,029 WARNING:  * Debugger is active!
2025-06-11 17:22:39,064 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:25:36,211 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\models.py', reloading
2025-06-11 17:25:36,299 INFO:  * Restarting with stat
2025-06-11 17:25:37,478 WARNING:  * Debugger is active!
2025-06-11 17:25:37,498 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:26:12,937 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:26:12,995 INFO:  * Restarting with stat
2025-06-11 17:26:14,058 WARNING:  * Debugger is active!
2025-06-11 17:26:14,069 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:32:41,156 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\common.py', reloading
2025-06-11 17:32:41,308 INFO:  * Restarting with stat
2025-06-11 17:32:42,891 WARNING:  * Debugger is active!
2025-06-11 17:32:42,899 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:35:27,145 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:35:27,192 INFO:  * Restarting with stat
2025-06-11 17:35:28,037 WARNING:  * Debugger is active!
2025-06-11 17:35:28,044 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:35:38,893 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\config.py', reloading
2025-06-11 17:35:38,968 INFO:  * Restarting with stat
2025-06-11 17:35:40,049 WARNING:  * Debugger is active!
2025-06-11 17:35:40,057 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:36:05,029 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\common.py', reloading
2025-06-11 17:36:05,088 INFO:  * Restarting with stat
2025-06-11 17:36:05,971 WARNING:  * Debugger is active!
2025-06-11 17:36:05,978 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:36:22,134 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:36:22,219 INFO:  * Restarting with stat
2025-06-11 17:36:23,209 WARNING:  * Debugger is active!
2025-06-11 17:36:23,216 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:36:26,120 INFO: 1******** - - [11/Jun/2025 17:36:26] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:36:26,296 INFO: 1******** - - [11/Jun/2025 17:36:26] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:36:26,297 INFO: 1******** - - [11/Jun/2025 17:36:26] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:36:27,056 INFO: 1******** - - [11/Jun/2025 17:36:27] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 17:36:36,305 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:36:36,377 INFO:  * Restarting with stat
2025-06-11 17:36:37,306 WARNING:  * Debugger is active!
2025-06-11 17:36:37,314 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:36:38,796 INFO: 1******** - - [11/Jun/2025 17:36:38] "POST /auth/login HTTP/1.1" 200 -
2025-06-11 17:36:38,970 INFO: 1******** - - [11/Jun/2025 17:36:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 17:36:38,971 INFO: 1******** - - [11/Jun/2025 17:36:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 17:36:48,209 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:36:48,317 INFO:  * Restarting with stat
2025-06-11 17:36:49,210 WARNING:  * Debugger is active!
2025-06-11 17:36:49,219 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:37:18,244 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:37:18,326 INFO:  * Restarting with stat
2025-06-11 17:37:19,213 WARNING:  * Debugger is active!
2025-06-11 17:37:19,221 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:37:35,367 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:37:35,419 INFO:  * Restarting with stat
2025-06-11 17:37:36,308 WARNING:  * Debugger is active!
2025-06-11 17:37:36,317 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:37:55,695 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\blueprints\\auth.py', reloading
2025-06-11 17:37:55,777 INFO:  * Restarting with stat
2025-06-11 17:37:56,685 WARNING:  * Debugger is active!
2025-06-11 17:37:56,693 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:43:40,141 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 17:43:40,142 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 17:44:20,810 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://1********:5000
 * Running on http://***********:5000
2025-06-11 17:44:20,811 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 17:45:45,319 WARNING: LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:45:45,320 WARNING: [SECURITY] LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:45:45,320 INFO: LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:45:45,320 INFO: [SECURITY] LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:45:45,321 WARNING: CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:45:45,322 WARNING: [SECURITY] CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:45:45,322 INFO: IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
2025-06-11 17:45:45,323 INFO: [SECURITY] IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
2025-06-11 17:45:45,324 WARNING: BLOCKED_IP_ACCESS FAILURE - user:Unknown from ip:************* - Blocked IP attempted to access login page
2025-06-11 17:45:45,325 INFO: LOGOUT SUCCESS - user:testuser from ip:************* - User logged out
2025-06-11 17:46:14,132 WARNING: LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:46:14,132 WARNING: [SECURITY] LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:46:14,133 INFO: LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:46:14,133 INFO: [SECURITY] LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:46:14,134 WARNING: CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:46:14,134 WARNING: [SECURITY] CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:46:14,135 INFO: IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
2025-06-11 17:46:14,135 INFO: [SECURITY] IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
2025-06-11 17:46:14,136 WARNING: BLOCKED_IP_ACCESS FAILURE - user:Unknown from ip:************* - Blocked IP attempted to access login page
2025-06-11 17:46:14,137 INFO: LOGOUT SUCCESS - user:testuser from ip:************* - User logged out
2025-06-11 17:46:40,213 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\utils\\common.py', reloading
2025-06-11 17:46:40,267 INFO:  * Restarting with stat
2025-06-11 17:46:41,219 WARNING:  * Debugger is active!
2025-06-11 17:46:41,228 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:46:50,170 WARNING: LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:46:50,171 WARNING: [SECURITY] LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:46:50,172 INFO: LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:46:50,173 INFO: [SECURITY] LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:46:50,175 WARNING: CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:46:50,176 WARNING: [SECURITY] CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:46:50,177 INFO: IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
2025-06-11 17:46:50,177 INFO: [SECURITY] IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
2025-06-11 17:46:50,178 WARNING: BLOCKED_IP_ACCESS FAILURE - user:Unknown from ip:************* - Blocked IP attempted to access login page
2025-06-11 17:46:50,179 INFO: LOGOUT SUCCESS - user:testuser from ip:************* - User logged out
2025-06-11 17:47:12,443 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\__init__.py', reloading
2025-06-11 17:47:12,500 INFO:  * Restarting with stat
2025-06-11 17:47:13,405 WARNING:  * Debugger is active!
2025-06-11 17:47:13,412 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:47:25,597 WARNING: [SECURITY] LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:47:25,598 INFO: [SECURITY] LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:47:25,599 WARNING: [SECURITY] CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:47:25,599 INFO: [SECURITY] IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
2025-06-11 17:48:10,167 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\test_security_logging.py', reloading
2025-06-11 17:48:10,220 INFO:  * Restarting with stat
2025-06-11 17:48:11,057 WARNING:  * Debugger is active!
2025-06-11 17:48:11,064 INFO:  * Debugger PIN: 122-053-548
2025-06-11 17:48:22,312 WARNING: [SECURITY] LOGIN_ATTEMPT FAILURE - user:testuser from ip:************* - Invalid credentials
2025-06-11 17:48:22,313 INFO: [SECURITY] LOGIN_ATTEMPT SUCCESS - user:testuser from ip:************* - role:publisher
2025-06-11 17:48:22,314 WARNING: [SECURITY] CAPTCHA_FAILURE FAILURE - user:testuser from ip:************* - reCAPTCHA verification failed
2025-06-11 17:48:22,314 INFO: [SECURITY] IP_BLOCKED SUCCESS - user:testuser from ip:************* - Blocked for 30 minutes after 3 failed attempts
