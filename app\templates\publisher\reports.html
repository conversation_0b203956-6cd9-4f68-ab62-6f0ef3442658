{% extends "base.html" %}

{% block title %}Reports & Suggestions - Publisher{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-flag"></i> Reports & Suggestions</h1>
                <div>
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('publisher.reports', type='abuse') }}"
                           class="btn btn-outline-primary {{ 'active' if report_type == 'abuse' else '' }}">
                            <i class="bi bi-shield-exclamation"></i> Abuse Reports
                        </a>
                        <a href="{{ url_for('publisher.reports', type='suggestions') }}"
                           class="btn btn-outline-info {{ 'active' if report_type == 'suggestions' else '' }}">
                            <i class="bi bi-lightbulb"></i> Suggestions
                        </a>
                    </div>
                    <a href="{{ url_for('publisher.dashboard') }}" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Filters -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <input type="hidden" name="type" value="{{ report_type }}">
                        <div class="col-md-4">
                            <label for="status" class="form-label">Status Filter:</label>
                            <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                                <option value="">All Statuses</option>
                                <option value="pending" {{ 'selected' if status_filter == 'pending' else '' }}>Pending</option>
                                <option value="reviewed" {{ 'selected' if status_filter == 'reviewed' else '' }}>Reviewed</option>
                                <option value="resolved" {{ 'selected' if status_filter == 'resolved' else '' }}>Resolved</option>
                                <option value="dismissed" {{ 'selected' if status_filter == 'dismissed' else '' }}>Dismissed</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Reports Table -->
            {% if reports %}
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="5%">ID</th>
                                    <th width="15%">{{ 'App' if report_type == 'abuse' else 'Type' }}</th>
                                    <th width="12%">{{ 'Report Type' if report_type == 'abuse' else 'Category' }}</th>
                                    <th width="25%">Reason</th>
                                    <th width="12%">Submitted By</th>
                                    <th width="10%">Status</th>
                                    <th width="12%">Created</th>
                                    <th width="9%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in reports %}
                                <tr>
                                    <td>{{ report.id }}</td>
                                    <td>
                                        {% if report_type == 'abuse' %}
                                            {% if report.app_name %}
                                                <a href="{{ url_for('main.app_detail', app_id=report.app_id) }}" target="_blank">
                                                    {{ report.app_name }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">App #{{ report.app_id }}</span>
                                            {% endif %}
                                        {% else %}
                                            {{ report.suggestion_type or 'General' }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ report.report_type if report_type == 'abuse' else report.category }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 250px;" title="{{ report.get('reason', report.get('description', 'N/A')) }}">
                                            {% set reason_text = report.get('reason', report.get('description', 'N/A')) %}
                                            {{ reason_text[:100] if reason_text != 'N/A' else 'N/A' }}{% if reason_text != 'N/A' and reason_text|length > 100 %}...{% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        {% if report.username %}
                                            {{ report.username }}
                                        {% else %}
                                            <span class="text-muted">Anonymous</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if report.status == 'pending' else 'success' if report.status == 'resolved' else 'secondary' }}">
                                            {{ report.status.title() }}
                                        </span>
                                    </td>
                                    <td>{{ report.created_at | datetime('%m/%d/%Y %H:%M') }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewReport({{ report.id }}, '{{ report_type }}')"
                                                data-bs-toggle="modal" data-bs-target="#reportModal">
                                            <i class="bi bi-eye"></i> View
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-inbox display-1 text-muted"></i>
                <h3 class="mt-3">No {{ report_type }} found</h3>
                <p class="text-muted">No {{ report_type }} match your current filters.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Report Details Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="bi bi-info-circle"></i> Report Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="reportModalBody">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle"></i> Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function viewReport(reportId, reportType) {
    // Find the report data from the table
    const reports = {{ reports | tojson }};
    const report = reports.find(r => r.id === reportId);

    if (!report) return;

    let content = `
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-info-circle"></i> Basic Information</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm table-borderless">
                            <tr><td class="fw-bold">ID:</td><td>${report.id}</td></tr>
                            <tr><td class="fw-bold">Status:</td><td><span class="badge bg-${report.status === 'pending' ? 'warning' : report.status === 'resolved' ? 'success' : 'secondary'}">${report.status.charAt(0).toUpperCase() + report.status.slice(1)}</span></td></tr>
                            <tr><td class="fw-bold">Created:</td><td>${new Date(report.created_at).toLocaleString()}</td></tr>
                            <tr><td class="fw-bold">IP Address:</td><td><code>${report.ip_address || 'N/A'}</code></td></tr>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="bi bi-flag"></i> Report Details</h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm table-borderless">
                            <tr><td class="fw-bold">Type:</td><td><span class="badge bg-secondary">${reportType === 'abuse' ? (report.report_type || 'N/A') : (report.category || 'N/A')}</span></td></tr>
                            <tr><td class="fw-bold">Reason:</td><td>${report.reason || report.description || 'N/A'}</td></tr>
                            <tr><td class="fw-bold">Submitted By:</td><td>${report.username ? '<span class="badge bg-info">' + report.username + '</span>' : '<span class="text-muted">Anonymous</span>'}</td></tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    `;

    if (report.description && reportType === 'abuse') {
        content += `
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-file-text"></i> Description</h6>
                </div>
                <div class="card-body">
                    <div class="p-2 rounded border">
                        ${report.description.replace(/\n/g, '<br>')}
                    </div>
                </div>
            </div>
        `;
    } else if (reportType === 'suggestions' && report.title) {
        content += `
            <div class="card mb-3">
                <div class="card-header ">
                    <h6 class="mb-0"><i class="bi bi-file-text"></i> Suggestion Title</h6>
                </div>
                <div class="card-body">
                    <div class="p-2 rounded border">
                        ${report.title}
                    </div>
                </div>
            </div>
        `;
    }

    if (report.decrypted_data) {
        content += `
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="bi bi-shield-lock"></i> Encrypted Data (Decrypted)</h6>
                    <small class="text-muted">This data was encrypted for security purposes</small>
                </div>
                <div class="card-body">
                    <div class="bg-dark text-light p-3 rounded">
                        <pre class="mb-0 text-light" style="font-size: 0.9em; line-height: 1.4;">${JSON.stringify(report.decrypted_data, null, 2)}</pre>
                    </div>
                </div>
            </div>
        `;
    }

    document.getElementById('reportModalBody').innerHTML = content;
}
</script>
{% endblock %}
