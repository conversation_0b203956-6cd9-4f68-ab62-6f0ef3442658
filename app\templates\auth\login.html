{% extends "base.html" %}

{% block title %}Login - PEPE Store{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card">
                <div class="card-header text-center">
                    <h3><i class="bi bi-person-lock"></i> Login</h3>
                    <p class="text-muted mb-0">Publishers & Administrators</p>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>

                        <!-- reCAPTCHA -->
                        <div class="mb-3 text-center">
                            <div class="recaptcha-wrapper">
                              <div class="g-recaptcha"
                                   data-sitekey="{{ config.RECAPTCHA_SITE_KEY }}"
                                   data-theme="dark">
                              </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right"></i> Login
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center text-muted">
                    <small>
                        Access restricted to authorized publishers and administrators only.<br>
                        <a href="{{ url_for('main.index') }}" class="text-decoration-none">← Back to Store</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- reCAPTCHA Script -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>
{% endblock %}
