{% extends "base.html" %}

{% block title %}Shortlinks Management - Admin{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-link-45deg"></i> Shortlinks Management</h1>
                <div>
                    <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#createShortlinkModal">
                        <i class="bi bi-plus"></i> Create Shortlink
                    </button>
                    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-primary">{{ shortlinks|length }}</h3>
                            <small class="text-muted">Total Shortlinks</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-success">{{ shortlinks|selectattr('is_active')|list|length }}</h3>
                            <small class="text-muted">Active</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-warning">{{ shortlinks|rejectattr('is_active')|list|length }}</h3>
                            <small class="text-muted">Inactive</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h3 class="text-info">{{ shortlinks|sum(attribute='click_count') or 0 }}</h3>
                            <small class="text-muted">Total Clicks</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Shortlinks Table -->
            {% if shortlinks %}
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Short Code</th>
                                    <th>Title</th>
                                    <th>Original URL</th>
                                    <th>Created By</th>
                                    <th>App</th>
                                    <th>Clicks</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for shortlink in shortlinks %}
                                <tr>
                                    <td>
                                        <code>{{ shortlink.short_code }}</code>
                                        <br>
                                        <small class="text-muted">
                                            <a href="{{ request.host_url }}s/{{ shortlink.short_code }}" target="_blank">
                                                {{ request.host_url }}s/{{ shortlink.short_code }}
                                            </a>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 150px;" title="{{ shortlink.title or 'No title' }}">
                                            {{ shortlink.title or '<em>No title</em>' }}
                                        </div>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ shortlink.original_url }}">
                                            <a href="{{ shortlink.original_url }}" target="_blank">
                                                {{ shortlink.original_url }}
                                            </a>
                                        </div>
                                    </td>
                                    <td>{{ shortlink.username or 'Unknown' }}</td>
                                    <td>
                                        {% if shortlink.app_name %}
                                            <a href="{{ url_for('main.app_detail', app_id=shortlink.app_id) }}" target="_blank">
                                                {{ shortlink.app_name }}
                                            </a>
                                        {% else %}
                                            <span class="text-muted">None</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ shortlink.click_count or 0 }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if shortlink.is_active else 'secondary' }}">
                                            {{ 'Active' if shortlink.is_active else 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>{{ shortlink.created_at | datetime('%m/%d/%Y') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-{{ 'warning' if shortlink.is_active else 'success' }}"
                                                    onclick="toggleShortlink({{ shortlink.id }}, {{ shortlink.is_active|lower }})"
                                                    title="{{ 'Deactivate' if shortlink.is_active else 'Activate' }}">
                                                <i class="bi bi-{{ 'pause' if shortlink.is_active else 'play' }}"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="deleteShortlink({{ shortlink.id }}, '{{ shortlink.short_code }}')"
                                                    title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-link display-1 text-muted"></i>
                <h3 class="mt-3">No shortlinks found</h3>
                <p class="text-muted">No shortlinks have been created yet.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Create Shortlink Modal -->
<div class="modal fade" id="createShortlinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Shortlink</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createShortlinkForm">
                    <div class="mb-3">
                        <label for="original_url" class="form-label">Original URL *</label>
                        <input type="url" class="form-control" id="original_url" required
                               placeholder="https://example.com">
                    </div>
                    <div class="mb-3">
                        <label for="title" class="form-label">Title</label>
                        <input type="text" class="form-control" id="title"
                               placeholder="Optional title for the shortlink">
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" rows="2"
                                  placeholder="Optional description"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="custom_code" class="form-label">Custom Code</label>
                        <input type="text" class="form-control" id="custom_code"
                               placeholder="Optional custom short code" maxlength="50"
                               pattern="[a-zA-Z0-9]{3,50}" title="3-50 characters, letters and numbers only">
                        <div class="form-text">Leave empty for auto-generated code. 3-50 characters, English letters and numbers only.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createShortlink()">Create Shortlink</button>
            </div>
        </div>
    </div>
</div>

<script>
function createShortlink() {
    const formData = {
        original_url: document.getElementById('original_url').value,
        title: document.getElementById('title').value,
        description: document.getElementById('description').value,
        custom_code: document.getElementById('custom_code').value
    };

    if (!formData.original_url) {
        alert('Original URL is required');
        return;
    }

    // Validate custom code if provided
    if (formData.custom_code) {
        if (formData.custom_code.length < 3 || formData.custom_code.length > 50) {
            alert('Custom code must be between 3 and 50 characters');
            return;
        }
        if (!/^[a-zA-Z0-9]+$/.test(formData.custom_code)) {
            alert('Custom code can only contain English letters and numbers');
            return;
        }
    }

    fetch('/admin/shortlinks/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Shortlink created successfully!\nShort URL: ${data.short_url}`);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the shortlink.');
    });
}

function toggleShortlink(shortlinkId, currentStatus) {
    const action = currentStatus ? 'deactivate' : 'activate';
    
    if (!confirm(`Are you sure you want to ${action} this shortlink?`)) {
        return;
    }
    
    fetch(`/admin/shortlinks/toggle/${shortlinkId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the shortlink.');
    });
}

function deleteShortlink(shortlinkId, shortCode) {
    if (!confirm(`Are you sure you want to delete shortlink "${shortCode}"? This action cannot be undone.`)) {
        return;
    }
    
    fetch(`/admin/shortlinks/delete/${shortlinkId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the shortlink.');
    });
}
</script>
{% endblock %}
