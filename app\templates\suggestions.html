{% extends "base.html" %}

{% block title %}Submit Suggestion - PEPE Store{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold text-primary">
                    <i class="bi bi-lightbulb"></i> Submit Suggestion
                </h1>
                <p class="lead text-muted">
                    Help us improve PEPE Store by sharing your ideas, reporting bugs, or suggesting new features.
                </p>
            </div>

            <!-- Suggestion Form -->
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form id="suggestionForm">
                        <!-- Category Selection -->
                        <div class="mb-4">
                            <label for="category" class="form-label fw-bold">
                                <i class="bi bi-tags"></i> Category *
                            </label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">Select a category...</option>
                                {% for category in categories %}
                                <option value="{{ category }}">
                                    {% if category == 'feature_request' %}
                                        🚀 Feature Request
                                    {% elif category == 'bug_report' %}
                                        🐛 Bug Report
                                    {% elif category == 'ui_improvement' %}
                                        🎨 UI Improvement
                                    {% elif category == 'performance' %}
                                        ⚡ Performance
                                    {% elif category == 'security' %}
                                        🔒 Security
                                    {% elif category == 'content_request' %}
                                        📦 Content Request
                                    {% elif category == 'other' %}
                                        💭 Other
                                    {% else %}
                                        {{ category.replace('_', ' ').title() }}
                                    {% endif %}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="form-text">Choose the category that best describes your suggestion.</div>
                        </div>

                        <!-- Priority Selection -->
                        <div class="mb-4">
                            <label for="priority" class="form-label fw-bold">
                                <i class="bi bi-exclamation-triangle"></i> Priority
                            </label>
                            <select class="form-select" id="priority" name="priority">
                                <option value="low">🟢 Low - Nice to have</option>
                                <option value="normal" selected>🟡 Normal - Would be helpful</option>
                                <option value="high">🟠 High - Important improvement</option>
                                <option value="urgent">🔴 Urgent - Critical issue</option>
                            </select>
                        </div>

                        <!-- Title -->
                        <div class="mb-4">
                            <label for="title" class="form-label fw-bold">
                                <i class="bi bi-card-heading"></i> Title *
                            </label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   placeholder="Brief, descriptive title for your suggestion" 
                                   maxlength="200" required>
                            <div class="form-text">Keep it short and descriptive (max 200 characters).</div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="description" class="form-label fw-bold">
                                <i class="bi bi-card-text"></i> Description *
                            </label>
                            <textarea class="form-control" id="description" name="description" 
                                      rows="6" placeholder="Provide detailed information about your suggestion..." 
                                      maxlength="2000" required></textarea>
                            <div class="form-text">
                                Please be as detailed as possible. For bug reports, include steps to reproduce. 
                                For feature requests, explain the use case and benefits. (max 2000 characters)
                            </div>
                        </div>

                        <!-- Guidelines -->
                        <div class="alert alert-info mb-4">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle"></i> Submission Guidelines
                            </h6>
                            <ul class="mb-0 small">
                                <li>Be specific and clear in your description</li>
                                <li>Check if your suggestion hasn't been submitted before</li>
                                <li>For bug reports, include your browser/OS information if relevant</li>
                                <li>For feature requests, explain why it would be valuable</li>
                                <li>Be respectful and constructive in your feedback</li>
                            </ul>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('main.index') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="bi bi-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-send"></i> Submit Suggestion
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <div id="messageContainer" class="mt-3" style="display: none;">
                <div id="messageAlert" class="alert" role="alert">
                    <span id="messageText"></span>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="row mt-5">
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body text-center">
                            <i class="bi bi-shield-check display-4 text-success mb-3"></i>
                            <h5>Privacy Protected</h5>
                            <p class="small text-muted">
                                Your suggestions are encrypted and stored securely. 
                                We only use this information to improve our platform.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body text-center">
                            <i class="bi bi-chat-dots display-4 text-primary mb-3"></i>
                            <h5>We Listen</h5>
                            <p class="small text-muted">
                                Every suggestion is reviewed by our team. 
                                We appreciate your feedback and strive to implement valuable ideas.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('suggestionForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('submitBtn');
    const messageContainer = document.getElementById('messageContainer');
    const messageAlert = document.getElementById('messageAlert');
    const messageText = document.getElementById('messageText');
    
    // Get form data
    const formData = {
        category: document.getElementById('category').value,
        title: document.getElementById('title').value.trim(),
        description: document.getElementById('description').value.trim(),
        priority: document.getElementById('priority').value
    };
    
    // Validate form
    if (!formData.category || !formData.title || !formData.description) {
        showMessage('Please fill in all required fields.', 'danger');
        return;
    }
    
    // Disable submit button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Submitting...';
    
    try {
        const response = await fetch('{{ url_for("main.suggestions") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage(result.message, 'success');
            document.getElementById('suggestionForm').reset();
        } else {
            showMessage(result.error || 'Failed to submit suggestion. Please try again.', 'danger');
        }
    } catch (error) {
        showMessage('Network error. Please check your connection and try again.', 'danger');
    } finally {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="bi bi-send"></i> Submit Suggestion';
    }
});

function showMessage(message, type) {
    const messageContainer = document.getElementById('messageContainer');
    const messageAlert = document.getElementById('messageAlert');
    const messageText = document.getElementById('messageText');
    
    messageAlert.className = `alert alert-${type}`;
    messageText.textContent = message;
    messageContainer.style.display = 'block';
    
    // Scroll to message
    messageContainer.scrollIntoView({ behavior: 'smooth' });
    
    // Auto-hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            messageContainer.style.display = 'none';
        }, 5000);
    }
}

// Character counter for title and description
document.getElementById('title').addEventListener('input', function() {
    updateCharCounter(this, 200);
});

document.getElementById('description').addEventListener('input', function() {
    updateCharCounter(this, 2000);
});

function updateCharCounter(element, maxLength) {
    const current = element.value.length;
    const remaining = maxLength - current;
    
    // Find or create counter element
    let counter = element.parentNode.querySelector('.char-counter');
    if (!counter) {
        counter = document.createElement('div');
        counter.className = 'char-counter form-text text-end';
        element.parentNode.appendChild(counter);
    }
    
    counter.textContent = `${current}/${maxLength}`;
    counter.className = `char-counter form-text text-end ${remaining < 50 ? 'text-warning' : remaining < 10 ? 'text-danger' : 'text-muted'}`;
}
</script>
{% endblock %}
