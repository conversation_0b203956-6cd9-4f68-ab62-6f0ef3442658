{% extends "base.html" %}

{% block title %}Edit {{ app.name }} - Publisher{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-pencil"></i> Edit App: {{ app.name }}</h1>
        <div>
            <a href="{{ url_for('main.app_detail', app_id=app.id) }}" class="btn btn-outline-info me-2">
                <i class="bi bi-eye"></i> View App
            </a>
            <a href="{{ url_for('publisher.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <form method="POST">
                        <!-- Basic Information -->
                        <h5 class="mb-3">Basic Information</h5>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">App Name *</label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="{{ app.name }}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="developer" class="form-label">Developer *</label>
                                <input type="text" class="form-control" id="developer" name="developer"
                                       value="{{ app.developer }}" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description *</label>
                            <input type="text" class="form-control" id="short_description" name="short_description"
                                   maxlength="200" value="{{ app.short_description }}" required>
                            <div class="form-text">Brief description shown in app listings (max 200 characters)</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Full Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="6" required>{{ app.description }}</textarea>
                            <div class="form-text">Detailed description shown on app page</div>
                        </div>

                        <!-- App Details -->
                        <h5 class="mb-3 mt-4">App Details</h5>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="version" class="form-label">Version *</label>
                                <input type="text" class="form-control" id="version" name="version"
                                       value="{{ app.version }}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category" class="form-label">Category *</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    {% for category in categories %}
                                    <option value="{{ category }}" {% if app.category == category %}selected{% endif %}>
                                        {{ category }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="price" class="form-label">Price ($)</label>
                                <input type="number" class="form-control" id="price" name="price"
                                       min="0" step="0.01" value="{{ app.price }}">
                                <div class="form-text">Set to 0 for free apps</div>
                            </div>
                        </div>

                        <!-- Download Method -->
                        <h5 class="mb-3 mt-4">Download Method</h5>

                        <!-- Current Download Status -->
                        <div class="alert alert-info mb-3">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle"></i> Current Download Method
                            </h6>
                            {% if app.file_path and app.external_url %}
                                <p class="mb-1"><strong>Both methods available:</strong></p>
                                <ul class="mb-0">
                                    <li><strong>File:</strong> {{ app.file_path.split('/')[-1] }} ({{ app.file_size | file_size }})</li>
                                    <li><strong>External URL:</strong> {{ app.external_url }}</li>
                                </ul>
                                <small class="text-muted">Users will see the external URL option first.</small>
                            {% elif app.file_path %}
                                <p class="mb-0">
                                    <strong>File Upload:</strong> {{ app.file_path.split('/')[-1] }}
                                    ({{ app.file_size | file_size }})
                                </p>
                            {% elif app.external_url %}
                                <p class="mb-0">
                                    <strong>External URL:</strong> {{ app.external_url }}
                                </p>
                            {% else %}
                                <p class="mb-0 text-warning">
                                    <strong>No download method configured!</strong> App will show as "Not Available"
                                </p>
                            {% endif %}
                        </div>

                        <!-- External URL Configuration -->
                        <div class="mb-3">
                            <label for="external_url" class="form-label">
                                <i class="bi bi-link-45deg"></i> External Download URL
                            </label>
                            <input type="url" class="form-control" id="external_url" name="external_url"
                                   value="{{ app.external_url or '' }}" placeholder="https://example.com/download">
                            <div class="form-text">
                                <strong>Optional:</strong> If provided, users will be redirected to this URL for download.
                                Leave empty to rely only on uploaded file.
                            </div>
                        </div>

                        <!-- File Management -->
                        {% if app.file_path %}
                        <div class="alert alert-secondary">
                            <h6><i class="bi bi-file-earmark"></i> Current File</h6>
                            <p class="mb-2">
                                <strong>File:</strong> {{ app.file_path.split('/')[-1] }}<br>
                                <strong>Size:</strong> {{ app.file_size | file_size }}
                            </p>
                            <div class="mb-2">
                                <label for="new_file" class="form-label">
                                    <i class="bi bi-upload"></i> Replace with New File (Optional)
                                </label>
                                <input type="file" class="form-control" id="new_file" name="new_file"
                                       accept=".exe,.msi,.zip,.rar,.7z,.tar,.gz,.deb,.rpm,.dmg,.pkg,.apk">
                                <div class="form-text">
                                    Leave empty to keep current file. Upload a new file to replace it.
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-warning">
                            <h6><i class="bi bi-exclamation-triangle"></i> No File Uploaded</h6>
                            <p class="mb-2">
                                This app doesn't have an uploaded file. You can upload one now or provide an external URL above.
                            </p>
                            <div class="mb-0">
                                <label for="new_file" class="form-label">
                                    <i class="bi bi-upload"></i> Upload File
                                </label>
                                <input type="file" class="form-control" id="new_file" name="new_file"
                                       accept=".exe,.msi,.zip,.rar,.7z,.tar,.gz,.deb,.rpm,.dmg,.pkg,.apk">
                                <div class="form-text">
                                    Upload a file to make it available for download.
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update App
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- App Info Sidebar -->
        <div class="col-lg-4">
            <!-- Current App Info -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> Current App Info</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        {% if app.icon_path %}
                        <img src="{{ url_for('main.uploaded_file', filename=app.icon_path) }}"
                             class="img-fluid rounded" style="max-width: 128px; max-height: 128px;" alt="{{ app.name }}">
                        {% else %}
                        <div class="bg-light border rounded d-flex align-items-center justify-content-center"
                             style="width: 128px; height: 128px; margin: 0 auto;">
                            <i class="bi bi-app display-4 text-muted"></i>
                        </div>
                        {% endif %}
                    </div>

                    <table class="table table-sm">
                        <tr>
                            <td><strong>Uploaded by:</strong></td>
                            <td>{{ app.uploaded_by }}</td>
                        </tr>
                        <tr>
                            <td><strong>Downloads:</strong></td>
                            <td>{{ app.downloads }}</td>
                        </tr>
                        <tr>
                            <td><strong>Created:</strong></td>
                            <td>{{ app.created_at | datetime('%m/%d/%Y') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Updated:</strong></td>
                            <td>{{ app.updated_at | datetime('%m/%d/%Y') }}</td>
                        </tr>
                        {% if app.file_path %}
                        <tr>
                            <td><strong>File Size:</strong></td>
                            <td>{{ app.file_size | file_size }}</td>
                        </tr>
                        {% endif %}
                        {% if app.external_url %}
                        <tr>
                            <td><strong>External URL:</strong></td>
                            <td><small>{{ app.external_url[:30] }}...</small></td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('main.app_detail', app_id=app.id) }}" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-eye"></i> View Public Page
                        </a>
                        {% if app | has_file or app.external_url %}
                        <a href="{{ app.id | download_url }}" class="btn btn-outline-success btn-sm"
                           {% if app | is_external %}target="_blank"{% endif %}>
                            <i class="bi bi-download"></i> Test Download
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Stats -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-graph-up"></i> Stats</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">{{ app.downloads }}</h4>
                                <small class="text-muted">Downloads</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">
                                {% if app.price == 0 %}FREE{% else %}&dollar;{{ "%.2f"|format(app.price) }}{% endif %}
                            </h4>
                            <small class="text-muted">Price</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Character counter for short description
document.getElementById('short_description').addEventListener('input', function(e) {
    const maxLength = 200;
    const currentLength = e.target.value.length;
    const remaining = maxLength - currentLength;

    let helpText = e.target.nextElementSibling;
    helpText.textContent = `Brief description shown in app listings (${remaining} characters remaining)`;

    if (remaining < 20) {
        helpText.classList.add('text-warning');
    } else {
        helpText.classList.remove('text-warning');
    }
});

// External URL validation
document.getElementById('external_url').addEventListener('input', function(e) {
    const url = e.target.value.trim();
    const helpText = e.target.nextElementSibling;

    if (url && !url.match(/^https?:\/\/.+/)) {
        helpText.textContent = 'Please enter a valid URL starting with http:// or https://';
        helpText.classList.add('text-warning');
        e.target.classList.add('is-invalid');
    } else {
        helpText.textContent = 'Optional: If provided, users will be redirected to this URL for download. Leave empty to rely only on uploaded file.';
        helpText.classList.remove('text-warning');
        e.target.classList.remove('is-invalid');
    }
});

// File upload validation
const newFileInput = document.getElementById('new_file');
if (newFileInput) {
    newFileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        const helpText = e.target.nextElementSibling;

        if (file) {
            const maxSize = 100 * 1024 * 1024; // 100MB
            const allowedTypes = ['.exe', '.msi', '.zip', '.rar', '.7z', '.tar', '.gz', '.deb', '.rpm', '.dmg', '.pkg', '.apk'];
            const fileName = file.name.toLowerCase();
            const fileExtension = fileName.substring(fileName.lastIndexOf('.'));

            if (file.size > maxSize) {
                helpText.textContent = 'File size must be less than 100MB';
                helpText.classList.add('text-danger');
                e.target.classList.add('is-invalid');
            } else if (!allowedTypes.includes(fileExtension)) {
                helpText.textContent = 'Invalid file type. Allowed: ' + allowedTypes.join(', ');
                helpText.classList.add('text-danger');
                e.target.classList.add('is-invalid');
            } else {
                helpText.textContent = `Selected: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                helpText.classList.remove('text-danger');
                helpText.classList.add('text-success');
                e.target.classList.remove('is-invalid');
                e.target.classList.add('is-valid');
            }
        } else {
            helpText.textContent = 'Upload a file to make it available for download.';
            helpText.classList.remove('text-danger', 'text-success');
            e.target.classList.remove('is-invalid', 'is-valid');
        }
    });
}
</script>
{% endblock %}
