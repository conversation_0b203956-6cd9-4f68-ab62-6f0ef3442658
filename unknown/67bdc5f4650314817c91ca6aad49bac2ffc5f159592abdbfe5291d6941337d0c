{% extends "base.html" %}

{% block title %}Blocked IPs - Admin{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-shield-x"></i> Blocked IP Addresses</h1>
                <div>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#blockIPModal">
                        <i class="bi bi-plus-circle"></i> Block IP
                    </button>
                    <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-danger text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ blocked_ips.total }}</h4>
                                    <p class="card-text">Total Blocked IPs</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-shield-x display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ blocked_ips.items | selectattr('status', 'equalto', 'active') | list | length }}</h4>
                                    <p class="card-text">Active Blocks</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-clock display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-secondary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ blocked_ips.items | selectattr('status', 'equalto', 'expired') | list | length }}</h4>
                                    <p class="card-text">Expired Blocks</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-check-circle display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-dark text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4 class="card-title">{{ blocked_ips.items | selectattr('is_permanent', 'equalto', 1) | list | length }}</h4>
                                    <p class="card-text">Permanent Blocks</p>
                                </div>
                                <div class="align-self-center">
                                    <i class="bi bi-ban display-4"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Blocked IPs Table -->
            {% if blocked_ips.items %}
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="15%">IP Address</th>
                                    <th width="12%">Status</th>
                                    <th width="10%">Attempts</th>
                                    <th width="20%">Reason</th>
                                    <th width="15%">Blocked At</th>
                                    <th width="15%">Blocked Until</th>
                                    <th width="13%">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ip in blocked_ips.items %}
                                <tr>
                                    <td>
                                        <code class="text-primary">{{ ip.ip_address }}</code>
                                    </td>
                                    <td>
                                        {% if ip.is_permanent %}
                                            <span class="badge bg-dark">Permanent</span>
                                        {% elif ip.status == 'active' %}
                                            <span class="badge bg-danger">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Expired</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-warning text-dark">{{ ip.attempts_count }}</span>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="{{ ip.reason }}">
                                            {{ ip.reason }}
                                        </div>
                                    </td>
                                    <td>{{ ip.blocked_at | datetime('%m/%d/%Y %H:%M') }}</td>
                                    <td>
                                        {% if ip.is_permanent %}
                                            <span class="text-muted">Never</span>
                                        {% else %}
                                            {{ ip.blocked_until | datetime('%m/%d/%Y %H:%M') }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ip.status == 'active' or ip.is_permanent %}
                                        <form method="POST" action="{{ url_for('admin.unblock_ip', ip_address=ip.ip_address) }}" style="display: inline;">
                                            <button type="submit" class="btn btn-sm btn-success" 
                                                    onclick="return confirm('Are you sure you want to unblock {{ ip.ip_address }}?')">
                                                <i class="bi bi-unlock"></i> Unblock
                                            </button>
                                        </form>
                                        {% else %}
                                        <span class="text-muted">Expired</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if blocked_ips.pages > 1 %}
                    <nav aria-label="Blocked IPs pagination">
                        <ul class="pagination justify-content-center">
                            {% if blocked_ips.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.blocked_ips', page=blocked_ips.prev_num) }}">Previous</a>
                            </li>
                            {% endif %}

                            {% for page_num in blocked_ips.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != blocked_ips.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.blocked_ips', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if blocked_ips.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.blocked_ips', page=blocked_ips.next_num) }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-shield-check display-1 text-success"></i>
                <h3 class="mt-3">No Blocked IPs</h3>
                <p class="text-muted">No IP addresses are currently blocked.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Block IP Modal -->
<div class="modal fade" id="blockIPModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="bi bi-shield-x"></i> Block IP Address</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('admin.block_ip_manual') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="ip_address" class="form-label">IP Address *</label>
                        <input type="text" class="form-control" id="ip_address" name="ip_address"
                               placeholder="***********" pattern="^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$" required>
                        <div class="form-text">Enter a valid IPv4 address</div>
                        <div class="alert alert-warning mt-2" role="alert">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Warning:</strong> Blocked IPs cannot access ANY part of the website.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="duration" class="form-label">Duration (minutes)</label>
                        <input type="number" class="form-control" id="duration" name="duration" 
                               value="30" min="1" max="43200">
                        <div class="form-text">How long to block the IP (max 30 days)</div>
                    </div>
                    <div class="mb-3">
                        <label for="reason" class="form-label">Reason</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="Reason for blocking this IP address">Manually blocked by admin</textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="is_permanent" name="is_permanent">
                        <label class="form-check-label" for="is_permanent">
                            Permanent block (ignores duration)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-shield-x"></i> Block IP
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
