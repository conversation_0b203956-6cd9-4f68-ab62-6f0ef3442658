(async () => {
  const fp = await FingerprintJS.load();
  const result = await fp.get();

  const fingerprint = result.visitorId;
  const browserData = result.components || {};

  const res = await fetch('/registerf', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      fingerprint: fingerprint,
      browser_data: browserData
    })
  });

  const data = await res.json();
  if (data.success) {
    window.location.href = "/";
  } else {
    alert("Fingerprint verification failed.");
  }
})();
