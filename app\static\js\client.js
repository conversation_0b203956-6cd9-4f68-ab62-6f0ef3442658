(async () => {
  try {
    const fp = await FingerprintJS.load();
    const result = await fp.get();

    const fingerprint = result.visitorId;
    const browserData = result.components || {};

    const res = await fetch('/registerf', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        fingerprint: fingerprint,
        browser_data: browserData
      })
    });

    const data = await res.json();
    if (data.success) {
      // Stop progress animation if available
      if (window.stopProgress) {
        window.stopProgress();
      }

      // Get the next URL from the global variable set by the template
      const nextUrl = window.NEXT_URL || '/';

      // Small delay to show completion
      setTimeout(() => {
        window.location.href = nextUrl;
      }, 500);
    } else {
      console.error('Fingerprint verification failed:', data.error);
      alert("Fingerprint verification failed. Please try again.");
      // Redirect to home page on failure
      window.location.href = "/";
    }
  } catch (error) {
    console.error('Fingerprinting error:', error);
    alert("An error occurred during verification. Please try again.");
    // Redirect to home page on error
    window.location.href = "/";
  }
})();
