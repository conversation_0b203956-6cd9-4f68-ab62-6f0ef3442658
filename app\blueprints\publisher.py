"""
Publisher blueprint for publisher routes
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from app.models import User, App, Screenshot, Shortlink, AbuseReport, Suggestion
from app.utils.auth import require_publisher_or_admin
from app.utils.common import sanitize_input, get_client_ip, validate_numeric_input
from app.utils.file_handlers import handle_file_upload, handle_image_upload

publisher_bp = Blueprint('publisher', __name__)


@publisher_bp.route('/dashboard')
@require_publisher_or_admin
def dashboard():
    """Publisher dashboard"""
    try:
        # Get user data
        user = User.get_by_id(session['user_id'])
        if not user:
            flash('User not found', 'error')
            return redirect(url_for('auth.login'))

        # Get publisher's apps
        user_apps = App.get_by_user(session['user_id'])

        # Get statistics
        total_apps = len(user_apps)
        total_downloads = sum(app.get('downloads', 0) for app in user_apps)

        return render_template('publisher/dashboard.html',
                             user=user,
                             user_apps=user_apps,
                             total_apps=total_apps,
                             total_downloads=total_downloads,
                             total_user_downloads=total_downloads)
    except Exception as e:
        flash(f'Error loading dashboard: {str(e)}', 'error')
        return redirect(url_for('main.index'))


@publisher_bp.route('/apps/add', methods=['GET', 'POST'])
@require_publisher_or_admin
def add_app():
    """Add new app"""
    if request.method == 'POST':
        try:
            # Get form data
            name = sanitize_input(request.form.get('name', ''))
            description = sanitize_input(request.form.get('description', ''))
            short_description = sanitize_input(request.form.get('short_description', ''))
            version = sanitize_input(request.form.get('version', ''))
            developer = sanitize_input(request.form.get('developer', ''))
            category = sanitize_input(request.form.get('category', ''))
            price = validate_numeric_input(request.form.get('price', 0), default=0, min_val=0)
            external_url = sanitize_input(request.form.get('external_url', ''))
            
            # Validate required fields
            if not all([name, description, short_description, version, developer, category]):
                flash('All required fields must be filled', 'error')
                return render_template('publisher/add_app.html', categories=App.get_categories())
            
            # Handle file upload
            file_path = None
            file_size = 0
            if 'app_file' in request.files and request.files['app_file'].filename:
                file_result, error = handle_file_upload(request.files['app_file'], 'apps')
                if error:
                    flash(f'File upload error: {error}', 'error')
                    return render_template('publisher/add_app.html', categories=App.get_categories())
                file_path = file_result
                # Get file size
                import os
                from app.config import Config
                full_path = os.path.join(Config.UPLOAD_FOLDER, file_path)
                if os.path.exists(full_path):
                    file_size = os.path.getsize(full_path)
            
            # Handle icon upload
            icon_path = None
            if 'icon' in request.files and request.files['icon'].filename:
                icon_result, error = handle_image_upload(request.files['icon'], 'icons')
                if error:
                    flash(f'Icon upload error: {error}', 'error')
                    return render_template('publisher/add_app.html', categories=App.get_categories())
                icon_path = icon_result
            
            # Create app (publishers can't set featured status)
            app_id = App.create(
                name=name,
                description=description,
                short_description=short_description,
                version=version,
                developer=developer,
                category=category,
                price=price,
                file_path=file_path,
                external_url=external_url,
                file_size=file_size,
                icon_path=icon_path,
                user_id=session['user_id'],
                is_featured=False
            )
            
            if app_id:
                # Handle screenshot uploads
                screenshot_files = request.files.getlist('screenshots')
                for i, screenshot_file in enumerate(screenshot_files):
                    if screenshot_file.filename:
                        screenshot_result, error = handle_image_upload(screenshot_file, 'screenshots')
                        if not error:
                            Screenshot.create(
                                app_id=app_id,
                                file_path=screenshot_result,
                                caption=sanitize_input(request.form.get(f'screenshot_caption_{i}', '')),
                                order_num=i
                            )
                
                flash('App created successfully!', 'success')
                return redirect(url_for('publisher.dashboard'))
            else:
                flash('Failed to create app', 'error')
                
        except Exception as e:
            flash(f'Error creating app: {str(e)}', 'error')
    
    return render_template('publisher/add_app.html', categories=App.get_categories())


@publisher_bp.route('/apps/edit/<int:app_id>', methods=['GET', 'POST'])
@require_publisher_or_admin
def edit_app(app_id):
    """Edit app (only by owner)"""
    try:
        app = App.get_by_id(app_id)
        if not app:
            flash('App not found', 'error')
            return redirect(url_for('publisher.dashboard'))

        # Check ownership
        if app['user_id'] != session['user_id']:
            flash('Not authorized to edit this app', 'error')
            return redirect(url_for('publisher.dashboard'))

        if request.method == 'POST':
            # Get form data
            name = sanitize_input(request.form.get('name', ''))
            description = sanitize_input(request.form.get('description', ''))
            short_description = sanitize_input(request.form.get('short_description', ''))
            version = sanitize_input(request.form.get('version', ''))
            developer = sanitize_input(request.form.get('developer', ''))
            category = sanitize_input(request.form.get('category', ''))
            price = validate_numeric_input(request.form.get('price', '0'), 'price')
            external_url = sanitize_input(request.form.get('external_url', ''))

            # Validation
            if not all([name, description, short_description, version, developer, category]):
                flash('All fields are required', 'error')
                return render_template('publisher/edit_app.html', app=app, categories=App.get_categories())

            if price is None:
                flash('Invalid price format', 'error')
                return render_template('publisher/edit_app.html', app=app, categories=App.get_categories())

            # Handle file upload
            file_path = app.get('file_path')  # Keep existing file by default
            file_size = app.get('file_size', 0)

            # Check for both 'file' (legacy) and 'new_file' (new edit form)
            uploaded_file = None
            if 'new_file' in request.files and request.files['new_file'].filename:
                uploaded_file = request.files['new_file']
            elif 'file' in request.files and request.files['file'].filename:
                uploaded_file = request.files['file']

            if uploaded_file:
                file_result, error = handle_file_upload(uploaded_file, 'apps')
                if error:
                    flash(f'File upload error: {error}', 'error')
                    return render_template('publisher/edit_app.html', app=app, categories=App.get_categories())

                # Delete old file if it exists
                if file_path:
                    import os
                    from app.config import Config
                    old_full_path = os.path.join(Config.UPLOAD_FOLDER, file_path)
                    if os.path.exists(old_full_path):
                        try:
                            os.remove(old_full_path)
                        except:
                            pass  # Continue even if old file deletion fails

                file_path = file_result
                # Get file size
                import os
                from app.config import Config
                full_path = os.path.join(Config.UPLOAD_FOLDER, file_path)
                if os.path.exists(full_path):
                    file_size = os.path.getsize(full_path)

            # Handle icon upload
            icon_path = app.get('icon_path')  # Keep existing icon by default
            if 'icon' in request.files and request.files['icon'].filename:
                icon_result, error = handle_image_upload(request.files['icon'], 'icons')
                if error:
                    flash(f'Icon upload error: {error}', 'error')
                    return render_template('publisher/edit_app.html', app=app, categories=App.get_categories())
                icon_path = icon_result

            # Prepare update data
            update_data = {
                'name': name,
                'description': description,
                'short_description': short_description,
                'version': version,
                'developer': developer,
                'category': category,
                'price': price,
                'file_path': file_path,
                'file_size': file_size,
                'icon_path': icon_path
            }

            # Handle external URL properly
            if external_url and external_url.strip():
                update_data['external_url'] = external_url.strip()
            else:
                update_data['external_url'] = None

            # Update app
            success = App.update(app_id, **update_data)

            if success:
                flash('App updated successfully', 'success')
                return redirect(url_for('publisher.dashboard'))
            else:
                flash('Failed to update app', 'error')

        return render_template('publisher/edit_app.html', app=app, categories=App.get_categories())

    except Exception as e:
        flash(f'Error editing app: {str(e)}', 'error')
        return redirect(url_for('publisher.dashboard'))


@publisher_bp.route('/apps/delete/<int:app_id>', methods=['POST'])
@require_publisher_or_admin
def delete_app(app_id):
    """Delete app (only by owner)"""
    if not session.get('user_id'):
        return jsonify({'success': False, 'error': 'Not logged in'}), 401

    try:
        # Verify ownership
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        if app['user_id'] != session['user_id']:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        # Delete the app
        success = App.delete(app_id, session['user_id'])
        if success:
            return jsonify({'success': True, 'message': 'App deleted successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to delete app'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@publisher_bp.route('/shortlinks')
@require_publisher_or_admin
def shortlinks():
    """Publisher shortlinks management"""
    try:
        shortlinks = Shortlink.get_by_user(session['user_id'])
        user_apps = App.get_by_user(session['user_id'])

        return render_template('publisher/shortlinks.html',
                             shortlinks=shortlinks,
                             user_apps=user_apps)
    except Exception as e:
        flash(f'Error loading shortlinks: {str(e)}', 'error')
        return redirect(url_for('publisher.dashboard'))


@publisher_bp.route('/shortlinks/create', methods=['POST'])
@require_publisher_or_admin
def create_shortlink():
    """Create shortlink (publisher)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        original_url = sanitize_input(data.get('original_url', ''))
        title = sanitize_input(data.get('title', ''))
        description = sanitize_input(data.get('description', ''))
        app_id = data.get('app_id')
        custom_code = sanitize_input(data.get('custom_code', ''))

        if not original_url:
            return jsonify({'success': False, 'error': 'Original URL is required'}), 400

        # Validate app ownership if app_id is provided
        if app_id:
            app = App.get_by_id(app_id)
            if not app or app['user_id'] != session['user_id']:
                return jsonify({'success': False, 'error': 'Invalid app selection'}), 400

        # Create shortlink
        shortlink_id, short_code = Shortlink.create(
            original_url=original_url,
            user_id=session['user_id'],
            app_id=app_id if app_id else None,
            title=title,
            description=description,
            custom_code=custom_code if custom_code else None
        )

        if shortlink_id:
            return jsonify({
                'success': True,
                'message': 'Shortlink created successfully',
                'shortlink_id': shortlink_id,
                'short_code': short_code
            })
        else:
            return jsonify({'success': False, 'error': short_code}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@publisher_bp.route('/shortlinks/delete/<int:shortlink_id>', methods=['POST'])
@require_publisher_or_admin
def delete_shortlink(shortlink_id):
    """Delete shortlink (publisher)"""
    try:
        success = Shortlink.delete(shortlink_id, session['user_id'])
        if success:
            return jsonify({'success': True, 'message': 'Shortlink deleted successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to delete shortlink or access denied'}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@publisher_bp.route('/shortlinks/update/<int:shortlink_id>', methods=['POST'])
@require_publisher_or_admin
def update_shortlink(shortlink_id):
    """Update shortlink (publisher)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        title = sanitize_input(data.get('title', ''))
        description = sanitize_input(data.get('description', ''))

        # Verify ownership first
        shortlink = Shortlink.get_by_id(shortlink_id)
        if not shortlink or shortlink['user_id'] != session['user_id']:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        success = Shortlink.update(
            shortlink_id,
            title=title,
            description=description
        )

        if success:
            return jsonify({'success': True, 'message': 'Shortlink updated successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to update shortlink'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@publisher_bp.route('/reports')
@require_publisher_or_admin
def reports():
    """Publisher reports management (view only)"""
    try:
        report_type = request.args.get('type', 'abuse')
        status_filter = request.args.get('status', '')

        if report_type == 'abuse':
            reports = AbuseReport.get_all(status=status_filter if status_filter else None)
        else:
            reports = Suggestion.get_all(status=status_filter if status_filter else None)

        return render_template('publisher/reports.html',
                             reports=reports,
                             report_type=report_type,
                             status_filter=status_filter)
    except Exception as e:
        flash(f'Error loading reports: {str(e)}', 'error')
        return redirect(url_for('publisher.dashboard'))


# Test route for publisher blueprint
@publisher_bp.route('/test')
@require_publisher_or_admin
def test_publisher():
    """Test route to verify publisher blueprint loads"""
    return jsonify({'blueprint': 'publisher', 'status': 'working', 'routes': ['/dashboard', '/apps/add', '/shortlinks']})
