#!/usr/bin/env python3
"""
App Store - Run Script
This script starts the Flask application with proper configuration.
"""

import os
import sys
from app import create_app
from app.config import Config

def main():
    """Main function to run the Flask application"""

    # Ensure SECRET_KEY is set
    if not os.environ.get('SECRET_KEY'):
        os.environ['SECRET_KEY'] = Config.SECRET_KEY

    # Create Flask app using application factory
    app = create_app()

    # Database will be auto-initialized by the app factory
    print("✓ Database initialization handled by app factory")

    # Check if running in development or production
    debug_mode = (
        os.environ.get('FLASK_ENV') == 'development' or
        os.environ.get('FLASK_DEBUG') == '1' or
        '--debug' in sys.argv
    )

    # Get port from environment or default to 5000
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')

    print("\n" + "="*50)
    print("🏪 PEPE STORE - Starting Server")
    print("="*50)
    print(f"Debug Mode: {'ON' if debug_mode else 'OFF'}")
    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"URL: http://localhost:{port}")
    print("\nDefault Admin Credentials:")
    print(f"Username: {Config.ADMIN_USERNAME}")
    print(f"Password: {Config.ADMIN_PASSWORD}")
    if Config.ADMIN_PASSWORD == 'admin123':
        print("⚠️  WARNING: Using default password! Change in production!")
    print("="*50)
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")

    try:
        # Run the Flask application
        app.run(
            debug=debug_mode,
            host=host,
            port=port,
            threaded=True,
            use_reloader=debug_mode
        )
    except KeyboardInterrupt:
        print("\n\n" + "="*50)
        print("🛑 Server stopped by user")
        print("="*50)
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        print("Check if port is already in use or permissions are correct")
        sys.exit(1)

if __name__ == '__main__':
    main()
