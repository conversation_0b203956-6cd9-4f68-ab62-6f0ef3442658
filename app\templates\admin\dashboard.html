{% extends "base.html" %}

{% block title %}Admin Dashboard - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-speedometer2"></i> Admin Dashboard</h1>
        <a href="{{ url_for('admin.add_app') }}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> Add New App
        </a>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_apps }}</h4>
                            <p class="mb-0">Total Apps</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-app-indicator display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_downloads }}</h4>
                            <p class="mb-0">Total Downloads</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-download display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_users }}</h4>
                            <p class="mb-0">Total Users</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-people display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ recent_admin_logs|length }}</h4>
                            <p class="mb-0">Admin Actions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-shield-check display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ blocked_ips_count or 0 }}</h4>
                            <p class="mb-0">Blocked IPs</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-shield-x display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Apps -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Apps</h5>
                    <a href="{{ url_for('admin.apps') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_apps %}
                    <div class="list-group list-group-flush">
                        {% for app in recent_apps %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ app.name }}</h6>
                                <small class="text-muted">{{ app.developer }} • {{ app.category }}</small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted d-block">{{ app.created_at | datetime('%m/%d/%Y') }}</small>
                                <span class="badge bg-secondary">{{ app.downloads }} downloads</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No apps yet</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Users -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Users</h5>
                    <a href="{{ url_for('admin.users') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_users %}
                    <div class="list-group list-group-flush">
                        {% for user in recent_users %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ user.username }}</h6>
                                <small class="text-muted">{{ user.email }} • {{ user.role }}</small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted d-block">{{ user.created_at | datetime('%m/%d/%Y') }}</small>
                                <span class="badge {% if user.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if user.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No users yet</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Section -->
    <div class="row mt-4">
        <!-- Admin Logs -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Admin Logs</h5>
                    <a href="{{ url_for('admin.logs', type='admin') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_admin_logs %}
                    <div class="list-group list-group-flush">
                        {% for log in recent_admin_logs %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ log.action }}</h6>
                                    <p class="mb-1 text-muted">{{ log.details }}</p>
                                    <small class="text-muted">by {{ log.username }}</small>
                                </div>
                                <small class="text-muted">{{ log.timestamp | datetime('%m/%d %H:%M') }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No admin logs yet</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Login Logs -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Login Logs</h5>
                    <a href="{{ url_for('admin.logs', type='login') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_login_logs %}
                    <div class="list-group list-group-flush">
                        {% for log in recent_login_logs %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ log.username }}</h6>
                                <small class="text-muted">{{ log.ip_address }}</small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted d-block">{{ log.timestamp | datetime('%m/%d %H:%M') }}</small>
                                <span class="badge {% if log.success %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if log.success %}Success{% else %}Failed{% endif %}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center">No login logs yet</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.add_app') }}" class="btn btn-success w-100">
                                <i class="bi bi-plus-circle"></i> Add App
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.apps') }}" class="btn btn-primary w-100">
                                <i class="bi bi-list"></i> Manage Apps
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.add_user') }}" class="btn btn-success w-100">
                                <i class="bi bi-person-plus"></i> Add User
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.users') }}" class="btn btn-warning w-100">
                                <i class="bi bi-people"></i> Manage Users
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.logs') }}" class="btn btn-secondary w-100">
                                <i class="bi bi-journal-text"></i> View Logs
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.reports') }}" class="btn btn-danger w-100">
                                <i class="bi bi-flag"></i> Reports
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.shortlinks') }}" class="btn btn-info w-100">
                                <i class="bi bi-link-45deg"></i> Shortlinks
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('admin.blocked_ips') }}" class="btn btn-dark w-100">
                                <i class="bi bi-shield-x"></i> Blocked IPs
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('main.index') }}" class="btn btn-outline-info w-100">
                                <i class="bi bi-house"></i> View Store
                            </a>
                        </div>
                        <div class="col-md-2 mb-2">
                            <a href="{{ url_for('auth.logout') }}" class="btn btn-outline-danger w-100">
                                <i class="bi bi-box-arrow-right"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
