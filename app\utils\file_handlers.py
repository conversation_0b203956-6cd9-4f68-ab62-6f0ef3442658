"""
File handling utilities for uploads and image processing
"""
import os
from datetime import datetime
from werkzeug.utils import secure_filename
from app.config import Config

try:
    from PIL import Image, ImageOps
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS


def handle_file_upload(file, upload_type='apps'):
    """Handle file upload and return file path"""
    if not file or file.filename == '':
        return None, "No file selected"

    if not allowed_file(file.filename):
        return None, f"File type not allowed. Allowed types: {', '.join(Config.ALLOWED_EXTENSIONS)}"

    # Create secure filename
    filename = secure_filename(file.filename)
    if not filename:
        return None, "Invalid filename"

    # Add timestamp to avoid conflicts
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
    filename = timestamp + filename

    # Create upload path
    upload_path = os.path.join(Config.UPLOAD_FOLDER, upload_type)
    os.makedirs(upload_path, exist_ok=True)

    file_path = os.path.join(upload_path, filename)

    try:
        file.save(file_path)
        # Return relative path for database storage
        return os.path.join(upload_type, filename), None
    except Exception as e:
        return None, f"Failed to save file: {str(e)}"


def compress_image(image_path, max_size=(512, 512), quality=85):
    """Compress and resize image to WebP format"""
    if not PIL_AVAILABLE:
        return False, "PIL not available for image processing"

    try:
        with Image.open(image_path) as img:
            # Convert to RGB if necessary (WebP supports RGB)
            if img.mode in ('RGBA', 'LA', 'P'):
                # For WebP, we can preserve transparency or convert to RGB
                if img.mode == 'RGBA':
                    # Keep RGBA for WebP transparency support
                    pass
                else:
                    # Convert P and LA to RGBA first
                    img = img.convert('RGBA')
            elif img.mode not in ('RGB', 'RGBA'):
                img = img.convert('RGB')

            # Resize image while maintaining aspect ratio
            img.thumbnail(max_size, Image.Resampling.LANCZOS)

            # Change file extension to .webp
            webp_path = os.path.splitext(image_path)[0] + '.webp'

            # Save as WebP with optimization
            img.save(webp_path, 'WEBP', quality=quality, optimize=True, method=6)

            # Remove original file if different from WebP
            if image_path != webp_path and os.path.exists(image_path):
                os.remove(image_path)

            return True, webp_path

    except Exception as e:
        return False, f"Image compression failed: {str(e)}"


def handle_image_upload(file, upload_type='icons', max_size=(512, 512), quality=85):
    """Handle image upload with WebP compression"""
    if not file or file.filename == '':
        return None, "No file selected"

    # Check if it's an image file
    allowed_image_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

    if file_ext not in allowed_image_extensions:
        return None, f"Invalid image format. Allowed: {', '.join(allowed_image_extensions)}"

    # Create secure filename with ASCII-only characters
    original_filename = file.filename
    # Remove any non-ASCII characters and replace with underscore
    safe_filename = ''.join(c if c.isascii() and (c.isalnum() or c in '.-_') else '_' for c in original_filename)
    filename = secure_filename(safe_filename)

    if not filename:
        filename = "image"

    # Change extension to .webp for compressed images
    base_name = filename.rsplit('.', 1)[0] if '.' in filename else filename

    # Add timestamp to avoid conflicts and ensure uniqueness
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
    temp_filename = f"{timestamp}{base_name}.tmp"
    final_filename = f"{timestamp}{base_name}.webp"

    # Create upload path
    upload_path = os.path.join(Config.UPLOAD_FOLDER, upload_type)
    os.makedirs(upload_path, exist_ok=True)

    temp_file_path = os.path.join(upload_path, temp_filename)
    final_file_path = os.path.join(upload_path, final_filename)

    try:
        # Save original file temporarily
        file.save(temp_file_path)

        # Compress image if PIL is available
        if PIL_AVAILABLE:
            success, result = compress_image(temp_file_path, max_size, quality)
            if success:
                # result is the WebP file path
                webp_path = result
                # Move to final location if needed
                if webp_path != final_file_path:
                    if os.path.exists(final_file_path):
                        os.remove(final_file_path)
                    os.rename(webp_path, final_file_path)
            else:
                # If compression fails, clean up and return error
                if os.path.exists(temp_file_path):
                    os.remove(temp_file_path)
                return None, f"Image compression failed: {result}"
        else:
            # If PIL not available, just rename temp file
            os.rename(temp_file_path, final_file_path)

        # Clean up temp file if it still exists
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)

        # Return relative path for database storage (use forward slashes for URLs)
        return f"{upload_type}/{final_filename}", None

    except Exception as e:
        # Clean up files if something went wrong
        for path in [temp_file_path, final_file_path]:
            if os.path.exists(path):
                try:
                    os.remove(path)
                except:
                    pass
        return None, f"Failed to save image: {str(e)}"
