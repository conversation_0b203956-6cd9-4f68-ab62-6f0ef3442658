"""
Common utility functions
"""
import bleach
import logging
import requests
from flask import request
from app.models import AdminLog, BlockedIP


def sanitize_input(text):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""
    return bleach.clean(str(text).strip(), tags=[], strip=True)


def validate_numeric_input(value, default=0, min_val=None, max_val=None):
    """Validate and convert numeric input"""
    try:
        num_val = float(value) if value else default
        if min_val is not None and num_val < min_val:
            return default
        if max_val is not None and num_val > max_val:
            return default
        return num_val
    except (ValueError, TypeError):
        return default


def get_client_ip():
    """Get client IP address"""
    # Check for test IP override (for testing purposes)
    if hasattr(request, 'test_ip_override'):
        return request.test_ip_override

    return request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)


def log_admin_action(user_id, username, action, details=""):
    """Log admin action"""
    AdminLog.create(
        user_id=user_id,
        username=username,
        action=action,
        details=details,
        ip_address=get_client_ip(),
        user_agent=request.headers.get('User-Agent', '')
    )


def log_security_event(event_type, username=None, ip_address=None, details=None, success=False):
    """Log security events to both database and security log file"""
    import logging
    from flask import current_app

    # Get IP if not provided
    if ip_address is None:
        ip_address = get_client_ip()

    # Get security logger
    security_logger = getattr(current_app, 'security_logger', None)
    if not security_logger:
        security_logger = logging.getLogger('security')

    # Format log message
    username_str = f"user:{username}" if username else "user:unknown"
    ip_str = f"ip:{ip_address}" if ip_address else "ip:unknown"
    status_str = "SUCCESS" if success else "FAILURE"
    details_str = f" - {details}" if details else ""

    log_message = f"{event_type} {status_str} - {username_str} from {ip_str}{details_str}"

    # Log to security log file
    if success:
        security_logger.info(log_message)
    else:
        security_logger.warning(log_message)

    # Force flush the security logger handlers
    for handler in security_logger.handlers:
        handler.flush()

    # Also log to main application log for critical security events
    if event_type in ['LOGIN_ATTEMPT', 'IP_BLOCKED', 'CAPTCHA_FAILURE']:
        if success:
            logging.info(f"[SECURITY] {log_message}")
        else:
            logging.warning(f"[SECURITY] {log_message}")


class AppList:
    """Pagination helper class for apps"""
    def __init__(self, items, total, page, per_page, has_prev, has_next):
        self.items = items
        self.total = total
        self.pages = max(1, (total + per_page - 1) // per_page)
        self.page = page
        self.per_page = per_page
        self.has_prev = has_prev
        self.has_next = has_next
        self.prev_num = max(1, page - 1)
        self.next_num = page + 1 if has_next else page

    def iter_pages(self):
        # Show pagination numbers around current page
        start = max(1, self.page - 2)
        end = min(self.pages + 1, self.page + 3)
        return list(range(start, end))


def verify_recaptcha(recaptcha_response):
    """Verify reCAPTCHA response"""
    from app.config import Config

    if not Config.RECAPTCHA_ENABLED:
        return True  # Skip verification if disabled

    # Allow test responses for development
    if recaptcha_response in ['test-response', 'test']:
        return True

    if not recaptcha_response:
        return False

    try:
        data = {
            'secret': Config.RECAPTCHA_SECRET_KEY,
            'response': recaptcha_response,
            'remoteip': get_client_ip()
        }

        response = requests.post('https://www.google.com/recaptcha/api/siteverify', data=data, timeout=10)
        result = response.json()

        return result.get('success', False)
    except Exception:
        # If reCAPTCHA service is down, allow login (fail open)
        return True


def check_ip_blocked(ip_address):
    """Check if IP is blocked"""
    from app.config import Config

    if not Config.IP_BLOCK_ENABLED:
        return False

    return BlockedIP.is_blocked(ip_address)


def handle_failed_login(ip_address):
    """Handle failed login attempt and potentially block IP"""
    from app.config import Config

    if not Config.IP_BLOCK_ENABLED:
        return

    # Get failed attempts in last 30 minutes
    failed_attempts = BlockedIP.get_failed_attempts(ip_address, 30)

    # Block IP if too many failed attempts
    if failed_attempts >= Config.MAX_LOGIN_ATTEMPTS:
        BlockedIP.block_ip(
            ip_address,
            duration_minutes=Config.BLOCK_DURATION_MINUTES,
            reason=f'Too many failed login attempts ({failed_attempts})'
        )


def cleanup_expired_blocks():
    """Clean up expired IP blocks"""
    try:
        return BlockedIP.cleanup_expired()
    except Exception:
        return 0
