"""
Custom template filters for the Flask application
"""
import markdown
from datetime import datetime
try:
    from flask import Markup
except ImportError:
    from markupsafe import Markup

from app.models import App


def register_filters(app):
    """Register all custom template filters"""
    
    @app.template_filter('markdown')
    def markdown_filter(text):
        """Convert markdown to HTML"""
        html = markdown.markdown(text or '', extensions=['fenced_code', 'codehilite'])
        return Markup(html)

    @app.template_filter('file_size')
    def file_size_filter(file_size):
        """Format file size"""
        return App.get_file_size_formatted(file_size)

    @app.template_filter('has_file')
    def has_file_filter(app):
        """Check if app has file"""
        return App.has_file(app)

    @app.template_filter('download_url')
    def download_url_filter(app_id):
        """Get download URL"""
        return App.get_download_url(app_id)

    @app.template_filter('is_external')
    def is_external_filter(app):
        """Check if download is external"""
        return App.is_external_download(app)

    @app.template_filter('datetime')
    def datetime_filter(timestamp, format='%B %d, %Y'):
        """Format datetime or string timestamp"""
        if not timestamp:
            return 'N/A'

        if isinstance(timestamp, str):
            try:
                # Try to parse string timestamp
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                    try:
                        dt = datetime.strptime(timestamp, fmt)
                        return dt.strftime(format)
                    except ValueError:
                        continue
                # If parsing fails, return the string as-is
                return timestamp
            except Exception:
                return timestamp
        elif hasattr(timestamp, 'strftime'):
            # It's already a datetime object
            return timestamp.strftime(format)
        else:
            return str(timestamp)
