import os
import secrets
from datetime import timedelta

class Config:
    # Generate a secure secret key - use environment variable in production
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///instance/app_store.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    CATEGORIES = [
    'RAT', 'Crypter', 'Stealer', 'Software',
    'Others'
    ]

    # Upload settings
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'zip', 'exe', 'rar', '7z'}

    # Logging settings
    LOG_FOLDER = 'logs'
    LOG_FILE = 'app_store.log'
    SECURITY_LOG_FILE = 'security.log'

    # Admin settings
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', '0xmrpepe')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')  # Default password for development
    if ADMIN_PASSWORD == 'admin123':
        import warnings
        warnings.warn('Using default admin password! Please set ADMIN_PASSWORD environment variable for better security.')

    # Enhanced Session Security Settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # Reduced from 24 hours
    SESSION_COOKIE_SECURE = os.environ.get('FLASK_ENV') == 'production'  # HTTPS only in production
    SESSION_COOKIE_HTTPONLY = True  # Prevent XSS access to cookies
    SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
    SESSION_COOKIE_NAME = 'pepe_session'  # Custom session name

    # Security Headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' cdn.jsdelivr.net;"
    }

    # Rate limiting settings
    RATELIMIT_STORAGE_URL = 'memory://'
    RATELIMIT_DEFAULT = "100 per hour"

    # Login attempt settings
    MAX_LOGIN_ATTEMPTS = 3  # Block after 3 failed attempts
    LOGIN_ATTEMPT_TIMEOUT = timedelta(minutes=15)

    # reCAPTCHA configuration
    RECAPTCHA_SITE_KEY = os.environ.get('RECAPTCHA_SITE_KEY', '6Lf_-lwrAAAAAB9k3jDQ7qm-pkXc7eRU5Z3PMLSo')
    RECAPTCHA_SECRET_KEY = os.environ.get('RECAPTCHA_SECRET_KEY', '6Lf_-lwrAAAAALk_sGMOa2C2ZUg3VMMTEQ4LFaHK')
    RECAPTCHA_ENABLED = os.environ.get('RECAPTCHA_ENABLED', 'true').lower() == 'true'

    # IP blocking configuration
    IP_BLOCK_ENABLED = os.environ.get('IP_BLOCK_ENABLED', 'true').lower() == 'true'
    BLOCK_DURATION_MINUTES = int(os.environ.get('BLOCK_DURATION_MINUTES', '30'))

    # Development mode - disable IP blocking for localhost
    DEVELOPMENT_MODE = os.environ.get('DEVELOPMENT_MODE', 'true').lower() == 'false'

    @staticmethod
    def init_app(flask_app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'apps'), exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'screenshots'), exist_ok=True)
        os.makedirs(Config.LOG_FOLDER, exist_ok=True)

        # Set additional Flask configuration
        flask_app.config['MAX_CONTENT_LENGTH'] = Config.MAX_CONTENT_LENGTH
