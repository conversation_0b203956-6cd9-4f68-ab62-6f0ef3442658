import os
import secrets
from datetime import timedelta

class Config:
    # Generate a secure secret key - use environment variable in production
    SECRET_KEY = '89516729fd4b3e019ba92695f16d107babe43b52902f63172a38863dc095e08cfe84a2fd2f0db5ff7b2f3d0536bfcbf8625defefa24b485cc88c6bc5dd0f73f06dbfbab946f18ffa7ce16d133667eee97953b66080a29ef713ecb350aa0114226ed90abca58c2cc2f5bd8a5f696157b35b09a43c8e32f0e250affc1f1f37d75cca4ce8cd9931220f254317c7dde6881e20ae42bf488bee873717d2c45c676cf852e5150235146f1457cb98c189f85661634de685f5715bab3042d6e775054c805d7728bde3e05e189e2083e6d77e45a885f4f1e59d7bbf3e7d58e630f366dab2ae36a257bd56376581cc9f83d1d2cf29ce91c1e52f416d0caace14abdb4459148ed9b3f648c6fb433d9496ed74bcfa6d58a57ac4c1ee800e8930e9bc5902681cbe0cdee2b3cab46b0cd92795fb4990bddcec0fbe199e423fdb596e42ce251eff7959d1094b1f04fc5d395d19107b0dcefeec0826466ab4e43484cc013eddc4f6c5c88fd6fcd25dfa80b29a51136183594b61f09b99a3cf2ac92e32b3661f9f8cad59628644e988df5e6b79885f6af287c7ad81439f485c690f1507021266d6092934f7ce4c0b9839e1022c81e2819c78f11f1a7b193c1bce22ac57b99d391bd0e67520e556b4fe7890bc058e2ab519b97168e052732de81295e64f7ce63032452399f2f900da8239a55ba0b80e0cea6a08ce084d7f64e8e81379e5c3893132ecea5313c33d9208546854236dd19e37e636f20b761405db54bb537ef78a212cf0b18e7557cb053954b6d97a25a97a47051ab81b492828bea0b6d1063be752593eb16c2fae1bbb625e4480e2733a5ea3b94b6b3cf89da6529fa511aad0ff43cadf0ba64bd3dd2cc303a2965db3ad7cca913194580e2e316bd2344d1974bcdb9cb6337939759e47a6295b0cca8ba886809b3533d600d8812221763e881575be9062794a105a8d0bd393c209f779dcb664e9540af1d5e9c836455a78146e'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///instance/app_store.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    CATEGORIES = [
    'RAT', 'Crypter', 'Stealer', 'Software',
    'Binders', 'Others'
    ]

    # Upload settings
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'zip', 'exe', 'rar', '7z'}

    # Logging settings
    LOG_FOLDER = 'logs'
    LOG_FILE = 'app_store.log'
    SECURITY_LOG_FILE = 'security.log'

    # Admin settings
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', '0xmrpepe')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')  # Default password for development
    if ADMIN_PASSWORD == 'admin123':
        import warnings
        warnings.warn('Using default admin password! Please set ADMIN_PASSWORD environment variable for better security.')

    # Enhanced Session Security Settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # Reduced from 24 hours
    SESSION_COOKIE_SECURE = os.environ.get('FLASK_ENV') == 'production'  # HTTPS only in production
    SESSION_COOKIE_HTTPONLY = True  # Prevent XSS access to cookies
    SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
    SESSION_COOKIE_NAME = 'pepe_session'  # Custom session name

    # Security Headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' cdn.jsdelivr.net;"
    }

    # Rate limiting settings
    RATELIMIT_STORAGE_URL = 'memory://'
    RATELIMIT_DEFAULT = "100 per hour"

    # Login attempt settings
    MAX_LOGIN_ATTEMPTS = 3  # Block after 3 failed attempts
    LOGIN_ATTEMPT_TIMEOUT = timedelta(minutes=15)

    # reCAPTCHA configuration
    RECAPTCHA_SITE_KEY = os.environ.get('RECAPTCHA_SITE_KEY', '6Lf_-lwrAAAAAB9k3jDQ7qm-pkXc7eRU5Z3PMLSo')
    RECAPTCHA_SECRET_KEY = os.environ.get('RECAPTCHA_SECRET_KEY', '6Lf_-lwrAAAAALk_sGMOa2C2ZUg3VMMTEQ4LFaHK')
    RECAPTCHA_ENABLED = os.environ.get('RECAPTCHA_ENABLED', 'true').lower() == 'true'

    # IP blocking configuration
    IP_BLOCK_ENABLED = os.environ.get('IP_BLOCK_ENABLED', 'true').lower() == 'true'
    BLOCK_DURATION_MINUTES = int(os.environ.get('BLOCK_DURATION_MINUTES', '30'))

    # Development mode - disable IP blocking for localhost
    DEVELOPMENT_MODE = os.environ.get('DEVELOPMENT_MODE', 'true').lower() == 'false'

    @staticmethod
    def init_app(flask_app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'apps'), exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'screenshots'), exist_ok=True)
        os.makedirs(Config.LOG_FOLDER, exist_ok=True)

        # Set additional Flask configuration
        flask_app.config['MAX_CONTENT_LENGTH'] = Config.MAX_CONTENT_LENGTH
