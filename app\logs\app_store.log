2025-06-10 23:00:17,384 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-10 23:00:17,386 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:00:27,060 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:27] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-10 23:00:27,335 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:27] "GET /gate HTTP/1.1" 200 -
2025-06-10 23:00:27,444 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:00:27,681 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:00:27,696 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:27] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-10 23:00:28,018 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:28] "POST /registerf HTTP/1.1" 200 -
2025-06-10 23:00:28,293 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:28] "GET / HTTP/1.1" 200 -
2025-06-10 23:00:28,355 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:00:28,620 INFO: 127.0.0.1 - - [10/Jun/2025 23:00:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:01:30,513 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*************:5000
2025-06-10 23:01:30,514 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:19:00,609 INFO: 127.0.0.1 - - [10/Jun/2025 23:19:00] "GET /login HTTP/1.1" 200 -
2025-06-10 23:19:00,920 INFO: 127.0.0.1 - - [10/Jun/2025 23:19:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:19:00,987 INFO: 127.0.0.1 - - [10/Jun/2025 23:19:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
