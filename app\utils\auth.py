"""
Authentication utilities and decorators
"""
import hmac
import hashlib
from functools import wraps
from flask import request, session, redirect, url_for, flash
from app.models import Fingerprint


def check_cookie_auth():
    """Simplified authentication check using fingerprinting"""
    # Allow certain paths without authentication
    allowed_paths = ['/registerf', '/gate', '/static', '/uploads']
    if any(request.path.startswith(path) for path in allowed_paths):
        return

    # Check if user is logged in via session (admin/publisher access)
    if session.get('user_id'):
        return  # User is logged in, allow access

    # For development/testing, allow access without fingerprint if no cookies are set
    # This prevents the app from being completely inaccessible
    uuid_token = request.cookies.get('uuid')
    fingerprint = request.cookies.get('fp')
    signature = request.cookies.get('sig')

    # If no fingerprint cookies at all, redirect to gate for setup
    if not any([uuid_token, fingerprint, signature]):
        return redirect('/gate')

    # If partial cookies, also redirect to gate
    if not all([uuid_token, fingerprint, signature]):
        return redirect('/gate')

    # Verify the signature
    try:
        fp_data = Fingerprint.get_by_uuid(uuid_token)
        if not fp_data or fp_data['fingerprint'] != fingerprint:
            return redirect('/gate')

        hmac_key = fp_data['hmac_key']
        expected_sig = hmac.new(hmac_key.encode(), f"{uuid_token}:{fingerprint}".encode(), hashlib.sha256).hexdigest()
        if not hmac.compare_digest(expected_sig, signature):
            return redirect('/gate')
    except Exception as e:
        # If fingerprint verification fails, redirect to gate
        print(f"Fingerprint verification error: {e}")  # Debug info
        return redirect('/gate')


def require_admin(f):
    """Decorator to require admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id') or session.get('role') != 'admin':
            flash('Access denied - Admin privileges required', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function


def require_login(f):
    """Decorator to require user login"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            flash('Please log in to access this page', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function


def require_publisher_or_admin(f):
    """Decorator to require publisher or admin access"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('user_id'):
            flash('Please log in to access this page', 'error')
            return redirect(url_for('auth.login'))
        
        user_role = session.get('role')
        if user_role not in ['publisher', 'admin']:
            flash('Access denied - Publisher or Admin privileges required', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return decorated_function
