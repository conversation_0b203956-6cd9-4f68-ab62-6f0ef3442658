"""
Admin blueprint for administrative routes
"""
from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from app.models import User, App, Screenshot, AdminLog, LoginLog, DownloadLog, AbuseReport, Suggestion, Shortlink, BlockedIP
from app.utils.auth import require_admin
from app.utils.common import sanitize_input, get_client_ip, log_admin_action, validate_numeric_input
from app.utils.file_handlers import handle_file_upload, handle_image_upload

admin_bp = Blueprint('admin', __name__)


@admin_bp.route('/dashboard')
@require_admin
def dashboard():
    """Admin dashboard"""
    try:
        # Get statistics
        total_apps = App.get_count()
        total_users = User.get_count()
        recent_apps = App.get_all(limit=5)
        recent_users = User.get_all(limit=5)
        
        # Get recent logs
        recent_admin_logs = AdminLog.get_all(limit=10)
        recent_login_logs = LoginLog.get_all(limit=10)

        # Get blocked IPs count
        blocked_ips_count = BlockedIP.get_count()

        return render_template('admin/dashboard.html',
                             total_apps=total_apps,
                             total_users=total_users,
                             blocked_ips_count=blocked_ips_count,
                             recent_apps=recent_apps,
                             recent_users=recent_users,
                             recent_admin_logs=recent_admin_logs,
                             recent_login_logs=recent_login_logs)
    except Exception as e:
        flash(f'Error loading dashboard: {str(e)}', 'error')
        return redirect(url_for('main.index'))


@admin_bp.route('/apps')
@require_admin
def apps():
    """Admin apps management"""
    try:
        page = max(1, request.args.get('page', 1, type=int))
        category = request.args.get('category', '').strip()
        search = request.args.get('search', '').strip()

        per_page = 20
        offset = (page - 1) * per_page

        apps_data = App.get_all(limit=per_page, offset=offset, category=category, search=search)
        total_apps = App.get_count(category=category, search=search)
        categories = App.get_categories()

        # Calculate pagination
        has_prev = page > 1
        has_next = offset + per_page < total_apps
        total_pages = (total_apps + per_page - 1) // per_page

        # Create pagination object
        class AppsPagination:
            def __init__(self, items, page, per_page, total, has_prev, has_next, total_pages):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.has_prev = has_prev
                self.has_next = has_next
                self.pages = total_pages
                self.prev_num = page - 1 if has_prev else None
                self.next_num = page + 1 if has_next else None

            def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
                last = self.pages
                for num in range(1, last + 1):
                    if num <= left_edge or \
                       (self.page - left_current - 1 < num < self.page + right_current) or \
                       num > last - right_edge:
                        yield num

        apps = AppsPagination(apps_data, page, per_page, total_apps, has_prev, has_next, total_pages)

        return render_template('admin/apps.html',
                             apps=apps,
                             categories=categories,
                             current_category=category,
                             search_term=search)
    except Exception as e:
        flash(f'Error loading apps: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/apps/add', methods=['GET', 'POST'])
@require_admin
def add_app():
    """Add new app"""
    if request.method == 'POST':
        try:
            # Get form data
            name = sanitize_input(request.form.get('name', ''))
            description = sanitize_input(request.form.get('description', ''))
            short_description = sanitize_input(request.form.get('short_description', ''))
            version = sanitize_input(request.form.get('version', ''))
            developer = sanitize_input(request.form.get('developer', ''))
            category = sanitize_input(request.form.get('category', ''))
            price = validate_numeric_input(request.form.get('price', 0), default=0, min_val=0)
            external_url = sanitize_input(request.form.get('external_url', ''))
            is_featured = bool(request.form.get('is_featured'))
            
            # Validate required fields
            if not all([name, description, short_description, version, developer, category]):
                flash('All required fields must be filled', 'error')
                return render_template('admin/add_app.html', categories=App.get_categories())
            
            # Handle file upload
            file_path = None
            file_size = 0
            if 'app_file' in request.files and request.files['app_file'].filename:
                file_result, error = handle_file_upload(request.files['app_file'], 'apps')
                if error:
                    flash(f'File upload error: {error}', 'error')
                    return render_template('admin/add_app.html', categories=App.get_categories())
                file_path = file_result
                # Get file size
                import os
                from app.config import Config
                full_path = os.path.join(Config.UPLOAD_FOLDER, file_path)
                if os.path.exists(full_path):
                    file_size = os.path.getsize(full_path)
            
            # Handle icon upload
            icon_path = None
            if 'icon' in request.files and request.files['icon'].filename:
                icon_result, error = handle_image_upload(request.files['icon'], 'icons')
                if error:
                    flash(f'Icon upload error: {error}', 'error')
                    return render_template('admin/add_app.html', categories=App.get_categories())
                icon_path = icon_result
            
            # Create app
            app_id = App.create(
                name=name,
                description=description,
                short_description=short_description,
                version=version,
                developer=developer,
                category=category,
                price=price,
                file_path=file_path,
                external_url=external_url,
                file_size=file_size,
                icon_path=icon_path,
                user_id=session['user_id'],
                is_featured=is_featured
            )
            
            if app_id:
                # Handle screenshot uploads
                screenshot_files = request.files.getlist('screenshots')
                for i, screenshot_file in enumerate(screenshot_files):
                    if screenshot_file.filename:
                        screenshot_result, error = handle_image_upload(screenshot_file, 'screenshots')
                        if not error:
                            Screenshot.create(
                                app_id=app_id,
                                file_path=screenshot_result,
                                caption=sanitize_input(request.form.get(f'screenshot_caption_{i}', '')),
                                order_num=i
                            )
                
                # Log admin action
                log_admin_action(
                    session['user_id'],
                    session['username'],
                    'CREATE_APP',
                    f'Created app: {name} (ID: {app_id})'
                )
                
                flash('App created successfully!', 'success')
                return redirect(url_for('admin.apps'))
            else:
                flash('Failed to create app', 'error')
                
        except Exception as e:
            flash(f'Error creating app: {str(e)}', 'error')
    
    return render_template('admin/add_app.html', categories=App.get_categories())


@admin_bp.route('/users')
@require_admin
def users():
    """Admin users management"""
    try:
        users = User.get_all()

        # Add apps data for each user
        for user in users:
            user['apps'] = App.get_by_user(user['id'])

        return render_template('admin/users.html', users=users)
    except Exception as e:
        flash(f'Error loading users: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/reports')
@require_admin
def reports():
    """Admin reports management"""
    try:
        report_type = request.args.get('type', 'abuse')
        status_filter = request.args.get('status', '')

        if report_type == 'abuse':
            reports = AbuseReport.get_all(status=status_filter if status_filter else None)
        else:
            reports = Suggestion.get_all(status=status_filter if status_filter else None)

        return render_template('admin/reports.html',
                             reports=reports,
                             report_type=report_type,
                             status_filter=status_filter)
    except Exception as e:
        flash(f'Error loading reports: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/reports/update_status/<int:report_id>', methods=['POST'])
@require_admin
def update_report_status(report_id):
    """Update report status"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        status = sanitize_input(data.get('status', ''))
        report_type = sanitize_input(data.get('type', 'abuse'))

        if status not in ['pending', 'reviewed', 'resolved', 'dismissed']:
            return jsonify({'success': False, 'error': 'Invalid status'}), 400

        if report_type == 'abuse':
            success = AbuseReport.update_status(report_id, status, session['user_id'])
        else:
            success = Suggestion.update_status(report_id, status, session['user_id'])

        if success:
            # Log admin action
            log_admin_action(
                session['user_id'],
                session['username'],
                'UPDATE_REPORT_STATUS',
                f'Updated {report_type} report {report_id} status to {status}'
            )
            return jsonify({
                'success': True,
                'message': f'{report_type.title()} status updated to {status} successfully'
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to update status'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/shortlinks')
@require_admin
def shortlinks():
    """Admin shortlinks management"""
    try:
        shortlinks = Shortlink.get_all()
        return render_template('admin/shortlinks.html', shortlinks=shortlinks)
    except Exception as e:
        flash(f'Error loading shortlinks: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/shortlinks/create', methods=['POST'])
@require_admin
def create_shortlink():
    """Create shortlink (admin)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        original_url = sanitize_input(data.get('original_url', ''))
        title = sanitize_input(data.get('title', ''))
        description = sanitize_input(data.get('description', ''))
        custom_code = sanitize_input(data.get('custom_code', ''))

        if not original_url:
            return jsonify({'success': False, 'error': 'Original URL is required'}), 400

        # Create shortlink
        shortlink_id, short_code = Shortlink.create(
            original_url=original_url,
            user_id=session['user_id'],
            title=title,
            description=description,
            custom_code=custom_code if custom_code else None
        )

        if shortlink_id:
            # Log admin action
            log_admin_action(
                session['user_id'],
                session['username'],
                'CREATE_SHORTLINK',
                f'Created shortlink: {short_code} -> {original_url}'
            )
            return jsonify({
                'success': True,
                'message': 'Shortlink created successfully',
                'shortlink_id': shortlink_id,
                'short_code': short_code
            })
        else:
            return jsonify({'success': False, 'error': short_code}), 400

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/shortlinks/delete/<int:shortlink_id>', methods=['POST'])
@require_admin
def delete_shortlink(shortlink_id):
    """Delete shortlink (admin)"""
    try:
        shortlink = Shortlink.get_by_id(shortlink_id)
        if not shortlink:
            return jsonify({'success': False, 'error': 'Shortlink not found'}), 404

        success = Shortlink.delete(shortlink_id)
        if success:
            # Log admin action
            log_admin_action(
                session['user_id'],
                session['username'],
                'DELETE_SHORTLINK',
                f'Deleted shortlink: {shortlink["short_code"]}'
            )
            return jsonify({'success': True, 'message': 'Shortlink deleted successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to delete shortlink'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/shortlinks/toggle/<int:shortlink_id>', methods=['POST'])
@require_admin
def toggle_shortlink(shortlink_id):
    """Toggle shortlink active status (admin)"""
    try:
        shortlink = Shortlink.get_by_id(shortlink_id)
        if not shortlink:
            return jsonify({'success': False, 'error': 'Shortlink not found'}), 404

        new_status = not shortlink.get('is_active', True)
        success = Shortlink.update_status(shortlink_id, new_status)

        if success:
            # Log admin action
            log_admin_action(
                session['user_id'],
                session['username'],
                'TOGGLE_SHORTLINK',
                f'{"Activated" if new_status else "Deactivated"} shortlink: {shortlink["short_code"]}'
            )
            return jsonify({
                'success': True,
                'message': f'Shortlink {"activated" if new_status else "deactivated"} successfully',
                'is_active': new_status
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to update shortlink'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/users/toggle_status/<int:user_id>', methods=['POST'])
@require_admin
def toggle_user_status(user_id):
    """Toggle user active/inactive status"""
    try:
        user = User.get_by_id(user_id)
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404

        # Don't allow deactivating self
        if user_id == session['user_id']:
            return jsonify({'success': False, 'error': 'Cannot deactivate your own account'}), 400

        new_status = not user.get('is_active', True)
        success = User.update_status(user_id, new_status)

        if success:
            # Log admin action
            log_admin_action(
                session['user_id'],
                session['username'],
                'TOGGLE_USER_STATUS',
                f'{"Activated" if new_status else "Deactivated"} user: {user["username"]}'
            )
            return jsonify({
                'success': True,
                'message': f'User {"activated" if new_status else "deactivated"} successfully',
                'is_active': new_status
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to update user status'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/logs')
@require_admin
def logs():
    """Admin logs"""
    try:
        log_type = request.args.get('type', 'admin')
        page = max(1, request.args.get('page', 1, type=int))
        per_page = 50
        offset = (page - 1) * per_page

        if log_type == 'admin':
            logs_data = AdminLog.get_all(limit=per_page, offset=offset)
            total_logs = AdminLog.get_count()
        elif log_type == 'login':
            logs_data = LoginLog.get_all(limit=per_page, offset=offset)
            total_logs = LoginLog.get_count()
        elif log_type == 'download':
            # Get download logs
            logs_data = DownloadLog.get_all(limit=per_page, offset=offset)
            total_logs = DownloadLog.get_count()
        else:
            # Default to admin logs
            logs_data = AdminLog.get_all(limit=per_page, offset=offset)
            total_logs = AdminLog.get_count()

        # Calculate pagination
        has_prev = page > 1
        has_next = offset + per_page < total_logs
        total_pages = (total_logs + per_page - 1) // per_page

        # Create pagination object
        class LogsPagination:
            def __init__(self, items, page, per_page, total, has_prev, has_next, total_pages):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.has_prev = has_prev
                self.has_next = has_next
                self.pages = total_pages
                self.prev_num = page - 1 if has_prev else None
                self.next_num = page + 1 if has_next else None

            def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
                last = self.pages
                for num in range(1, last + 1):
                    if num <= left_edge or \
                       (self.page - left_current - 1 < num < self.page + right_current) or \
                       num > last - right_edge:
                        yield num

        logs = LogsPagination(logs_data, page, per_page, total_logs, has_prev, has_next, total_pages)

        return render_template('admin/logs.html',
                             logs=logs,
                             log_type=log_type)

    except Exception as e:
        flash(f'Error loading logs: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/apps/edit/<int:app_id>', methods=['GET', 'POST'])
@require_admin
def edit_app(app_id):
    """Edit app"""
    try:
        app = App.get_by_id(app_id)
        if not app:
            flash('App not found', 'error')
            return redirect(url_for('admin.apps'))

        if request.method == 'POST':
            # Get form data
            name = sanitize_input(request.form.get('name', ''))
            description = sanitize_input(request.form.get('description', ''))
            short_description = sanitize_input(request.form.get('short_description', ''))
            version = sanitize_input(request.form.get('version', ''))
            developer = sanitize_input(request.form.get('developer', ''))
            category = sanitize_input(request.form.get('category', ''))
            price = validate_numeric_input(request.form.get('price', 0), default=0, min_val=0)
            external_url = sanitize_input(request.form.get('external_url', ''))
            is_featured = bool(request.form.get('is_featured'))

            # Get download method selection
            download_method = sanitize_input(request.form.get('download_method', ''))

            # Prepare update data
            update_data = {
                'name': name,
                'description': description,
                'short_description': short_description,
                'version': version,
                'developer': developer,
                'category': category,
                'price': price,
                'is_featured': is_featured
            }

            # Handle exclusive download method logic
            if download_method == 'external':
                # User chose external URL - remove file and set external URL
                if external_url and external_url.strip():
                    update_data['external_url'] = external_url.strip()
                    update_data['file_path'] = None
                    update_data['file_size'] = 0
                else:
                    flash('External URL is required when using external download method', 'error')
                    return render_template('admin/edit_app.html', app=app, categories=App.get_categories())
            elif download_method == 'file':
                # User chose file upload - remove external URL, keep existing file
                update_data['external_url'] = None
                # Keep existing file_path and file_size (don't modify them)
            else:
                # No method selected or invalid method - preserve current state
                # Don't modify file_path, file_size, or external_url
                pass

            # Update app
            success = App.update(app_id=app_id, **update_data)

            if success:
                # Log admin action
                log_admin_action(
                    session['user_id'],
                    session['username'],
                    'UPDATE_APP',
                    f'Updated app: {name} (ID: {app_id})'
                )
                flash('App updated successfully', 'success')
                return redirect(url_for('admin.apps'))
            else:
                flash('Failed to update app', 'error')

        return render_template('admin/edit_app.html', app=app, categories=App.get_categories())

    except Exception as e:
        flash(f'Error editing app: {str(e)}', 'error')
        return redirect(url_for('admin.apps'))


@admin_bp.route('/apps/delete/<int:app_id>', methods=['POST'])
@require_admin
def delete_app(app_id):
    """Delete app"""
    try:
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        success = App.delete(app_id)
        if success:
            # Log admin action
            log_admin_action(
                session['user_id'],
                session['username'],
                'DELETE_APP',
                f'Deleted app: {app["name"]} (ID: {app_id})'
            )
            flash('App deleted successfully', 'success')
            return redirect(url_for('admin.apps'))
        else:
            flash('Failed to delete app', 'error')
            return redirect(url_for('admin.apps'))

    except Exception as e:
        flash(f'Error deleting app: {str(e)}', 'error')
    
    return redirect(url_for('admin.apps'))


@admin_bp.route('/users/add', methods=['GET', 'POST'])
@require_admin
def add_user():
    """Add new user"""
    if request.method == 'POST':
        try:
            username = sanitize_input(request.form.get('username', ''))
            email = sanitize_input(request.form.get('email', ''))
            password = request.form.get('password', '')
            role = sanitize_input(request.form.get('role', 'publisher'))

            if not all([username, email, password]):
                flash('All fields are required', 'error')
                return render_template('admin/add_user.html')

            # Check if username exists
            if User.get_by_username(username):
                flash('Username already exists', 'error')
                return render_template('admin/add_user.html')

            # Create user
            user_id = User.create(username, email, password, role)
            if user_id:
                # Log admin action
                log_admin_action(
                    session['user_id'],
                    session['username'],
                    'CREATE_USER',
                    f'Created user: {username} (Role: {role})'
                )
                flash('User created successfully', 'success')
                return redirect(url_for('admin.users'))
            else:
                flash('Failed to create user', 'error')

        except Exception as e:
            flash(f'Error creating user: {str(e)}', 'error')

    return render_template('admin/add_user.html')


@admin_bp.route('/users/edit/<int:user_id>', methods=['GET', 'POST'])
@require_admin
def edit_user(user_id):
    """Edit user"""
    try:
        user = User.get_by_id(user_id)
        if not user:
            flash('User not found', 'error')
            return redirect(url_for('admin.users'))

        if request.method == 'POST':
            username = sanitize_input(request.form.get('username', ''))
            email = sanitize_input(request.form.get('email', ''))
            role = sanitize_input(request.form.get('role', ''))
            password = request.form.get('password', '').strip()

            # Validation
            if not username or not email or not role:
                flash('All fields except password are required', 'error')
                return render_template('admin/edit_user.html', user=user)

            if role not in ['admin', 'publisher']:
                flash('Invalid role selected', 'error')
                return render_template('admin/edit_user.html', user=user)

            # Check if username/email already exists (excluding current user)
            existing_user = User.get_by_username(username)
            if existing_user and existing_user['id'] != user_id:
                flash('Username already exists', 'error')
                return render_template('admin/edit_user.html', user=user)

            existing_user = User.get_by_email(email)
            if existing_user and existing_user['id'] != user_id:
                flash('Email already exists', 'error')
                return render_template('admin/edit_user.html', user=user)

            # Update user
            update_data = {
                'username': username,
                'email': email,
                'role': role
            }

            if password:  # Only update password if provided
                update_data['password'] = password

            success = User.update(user_id, **update_data)
            if success:
                # Log admin action
                log_admin_action(
                    session['user_id'],
                    session['username'],
                    'UPDATE_USER',
                    f'Updated user: {username}'
                )
                flash('User updated successfully', 'success')
                return redirect(url_for('admin.users'))
            else:
                flash('Failed to update user', 'error')

        return render_template('admin/edit_user.html', user=user)

    except Exception as e:
        flash(f'Error editing user: {str(e)}', 'error')
        return redirect(url_for('admin.users'))


@admin_bp.route('/users/delete/<int:user_id>', methods=['POST'])
@require_admin
def delete_user(user_id):
    """Delete user"""
    try:
        user = User.get_by_id(user_id)
        if not user:
            return jsonify({'success': False, 'error': 'User not found'}), 404

        # Don't allow deleting self
        if user_id == session['user_id']:
            return jsonify({'success': False, 'error': 'Cannot delete your own account'}), 400

        success = User.delete(user_id)
        if success:
            # Log admin action
            log_admin_action(
                session['user_id'],
                session['username'],
                'DELETE_USER',
                f'Deleted user: {user["username"]}'
            )
            return jsonify({'success': True, 'message': 'User deleted successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to delete user'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500


@admin_bp.route('/blocked-ips')
@require_admin
def blocked_ips():
    """Admin blocked IPs management"""
    try:
        page = max(1, request.args.get('page', 1, type=int))
        per_page = 20
        offset = (page - 1) * per_page

        blocked_ips_data = BlockedIP.get_all(limit=per_page, offset=offset)
        total_blocked = BlockedIP.get_count()

        # Calculate pagination
        has_prev = page > 1
        has_next = offset + per_page < total_blocked
        total_pages = (total_blocked + per_page - 1) // per_page

        # Create pagination object
        class BlockedIPsPagination:
            def __init__(self, items, page, per_page, total, has_prev, has_next, total_pages):
                self.items = items
                self.page = page
                self.per_page = per_page
                self.total = total
                self.has_prev = has_prev
                self.has_next = has_next
                self.pages = total_pages
                self.prev_num = page - 1 if has_prev else None
                self.next_num = page + 1 if has_next else None

            def iter_pages(self, left_edge=2, left_current=2, right_current=3, right_edge=2):
                last = self.pages
                for num in range(1, last + 1):
                    if num <= left_edge or \
                       (self.page - left_current - 1 < num < self.page + right_current) or \
                       num > last - right_edge:
                        yield num

        blocked_ips = BlockedIPsPagination(blocked_ips_data, page, per_page, total_blocked, has_prev, has_next, total_pages)

        return render_template('admin/blocked_ips.html', blocked_ips=blocked_ips)
    except Exception as e:
        flash(f'Error loading blocked IPs: {str(e)}', 'error')
        return redirect(url_for('admin.dashboard'))


@admin_bp.route('/blocked-ips/unblock/<ip_address>', methods=['POST'])
@require_admin
def unblock_ip(ip_address):
    """Unblock an IP address"""
    try:
        success = BlockedIP.unblock_ip(ip_address)
        if success:
            log_admin_action(
                session['user_id'],
                session['username'],
                'UNBLOCK_IP',
                f'Unblocked IP address: {ip_address}'
            )
            flash(f'IP address {ip_address} has been unblocked', 'success')
        else:
            flash('Failed to unblock IP address', 'error')
    except Exception as e:
        flash(f'Error unblocking IP: {str(e)}', 'error')

    return redirect(url_for('admin.blocked_ips'))


@admin_bp.route('/blocked-ips/block', methods=['POST'])
@require_admin
def block_ip_manual():
    """Manually block an IP address"""
    try:
        ip_address = sanitize_input(request.form.get('ip_address'))
        duration = int(request.form.get('duration', 30))
        reason = sanitize_input(request.form.get('reason', 'Manually blocked by admin'))
        is_permanent = request.form.get('is_permanent') == 'on'

        if not ip_address:
            flash('IP address is required', 'error')
            return redirect(url_for('admin.blocked_ips'))

        success = BlockedIP.block_ip(ip_address, duration, reason, is_permanent)
        if success:
            log_admin_action(
                session['user_id'],
                session['username'],
                'BLOCK_IP',
                f'Manually blocked IP: {ip_address} for {duration} minutes. Reason: {reason}'
            )

            # Check if the blocked IP is the current user's IP
            from app.utils.common import get_client_ip
            current_ip = get_client_ip()

            if ip_address == current_ip:
                flash(f'Warning: You have blocked your own IP address ({ip_address}). You will be disconnected after this request.', 'warning')
            else:
                flash(f'IP address {ip_address} has been blocked and cannot access the website', 'success')
        else:
            flash('Failed to block IP address', 'error')
    except Exception as e:
        flash(f'Error blocking IP: {str(e)}', 'error')

    return redirect(url_for('admin.blocked_ips'))


# Test route for admin blueprint
@admin_bp.route('/test')
@require_admin
def test_admin():
    """Test route to verify admin blueprint loads"""
    return jsonify({'blueprint': 'admin', 'status': 'working', 'routes': ['/dashboard', '/apps', '/users', '/reports', '/shortlinks', '/logs']})
